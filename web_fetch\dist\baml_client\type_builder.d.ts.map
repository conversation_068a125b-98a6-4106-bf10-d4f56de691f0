{"version": 3, "file": "type_builder.d.ts", "sourceRoot": "", "sources": ["../../baml_client/type_builder.ts"], "names": [], "mappings": "AAAA;;;;;;;;kGAQkG;AAYlG,OAAO,EAAE,SAAS,EAAE,MAAM,yBAAyB,CAAA;AACnD,OAAO,EAA+B,WAAW,EAAc,YAAY,EAAE,WAAW,EAAE,MAAM,+BAA+B,CAAA;AAG/H,MAAM,CAAC,OAAO,OAAO,WAAW;IAC5B,OAAO,CAAC,EAAE,CAAe;IAEzB,UAAU,EAAE,WAAW,CAAC,YAAY,EAAE,QAAQ,GAAG,SAAS,CAAC,CAAC;IAE5D,eAAe,EAAE,WAAW,CAAC,iBAAiB,EAAE,QAAQ,GAAG,SAAS,GAAG,WAAW,GAAG,UAAU,CAAC,CAAC;IAEjG,YAAY,EAAE,WAAW,CAAC,cAAc,EAAE,QAAQ,GAAG,KAAK,GAAG,aAAa,CAAC,CAAC;IAE5E,kBAAkB,EAAE,WAAW,CAAC,oBAAoB,EAAE,QAAQ,GAAG,SAAS,GAAG,aAAa,GAAG,QAAQ,CAAC,CAAC;IAEvG,oBAAoB,EAAE,WAAW,CAAC,sBAAsB,EAAE,QAAQ,GAAG,SAAS,GAAG,YAAY,GAAG,YAAY,CAAC,CAAC;IAE9G,cAAc,EAAE,WAAW,CAAC,gBAAgB,EAAE,SAAS,GAAG,SAAS,GAAG,eAAe,GAAG,UAAU,CAAC,CAAC;IAEpG,WAAW,EAAE,WAAW,CAAC,aAAa,EAAE,OAAO,GAAG,aAAa,GAAG,KAAK,GAAG,cAAc,GAAG,gBAAgB,GAAG,eAAe,GAAG,aAAa,CAAC,CAAC;;IA8C/I,IAAI;IAIJ,MAAM,IAAI,SAAS;IAInB,aAAa,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS;IAIvC,UAAU,CAAC,KAAK,EAAE,MAAM,GAAG,SAAS;IAIpC,WAAW,CAAC,KAAK,EAAE,OAAO,GAAG,SAAS;IAItC,GAAG,IAAI,SAAS;IAIhB,KAAK,IAAI,SAAS;IAIlB,IAAI,IAAI,SAAS;IAIjB,IAAI,CAAC,IAAI,EAAE,SAAS,GAAG,SAAS;IAIhC,IAAI,IAAI,SAAS;IAIjB,GAAG,CAAC,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,SAAS,GAAG,SAAS;IAIhD,KAAK,CAAC,KAAK,EAAE,SAAS,EAAE,GAAG,SAAS;IAIpC,QAAQ,CAAC,IAAI,SAAS,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,YAAY,CAAC,IAAI,CAAC;IAI7D,OAAO,CAAC,IAAI,SAAS,MAAM,EAAE,IAAI,EAAE,IAAI,GAAG,WAAW,CAAC,IAAI,CAAC;IAI3D,OAAO,CAAC,IAAI,EAAE,MAAM,GAAG,IAAI;CAG9B"}