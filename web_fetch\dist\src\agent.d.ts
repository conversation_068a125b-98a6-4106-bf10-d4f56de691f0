import { FetchUrlTool, ProcessContentTool, ExtractDataTool, SummarizeContentTool } from "../baml_client";
export interface Event {
    type: string;
    data: any;
    timestamp?: string;
}
export declare class Thread {
    events: Event[];
    constructor(events?: Event[]);
    addEvent(type: string, data: any): void;
    serializeForLLM(): string;
    getLastEvent(): Event | undefined;
    getEventsByType(type: string): Event[];
}
export type WebFetchTool = FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool;
export declare class WebFetchAgent {
    private webFetcher;
    constructor();
    handleNextStep(nextStep: WebFetchTool, thread: Thread): Promise<Thread>;
    private handleFetchUrl;
    private handleProcessContent;
    private handleExtractData;
    private handleSummarizeContent;
    agentLoop(thread: Thread): Promise<string>;
    getLatestContent(thread: Thread): string | null;
    getLatestProcessingResult(thread: Thread): string | null;
}
//# sourceMappingURL=agent.d.ts.map