{"version": 3, "file": "agent.js", "sourceRoot": "", "sources": ["../../src/agent.ts"], "names": [], "mappings": ";;;AAAA,gDAMwB;AACxB,6CAA0D;AAQ1D,MAAa,MAAM;IAGjB,YAAY,SAAkB,EAAE;QAFhC,WAAM,GAAY,EAAE,CAAC;QAGnB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;IACvB,CAAC;IAED,QAAQ,CAAC,IAAY,EAAE,IAAS;QAC9B,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;YACf,IAAI;YACJ,IAAI;YACJ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IACL,CAAC;IAED,eAAe;QACb,uCAAuC;QACvC,OAAO,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;IAC9C,CAAC;IAED,YAAY;QACV,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC;IAC7C,CAAC;IAED,eAAe,CAAC,IAAY;QAC1B,OAAO,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,IAAI,KAAK,IAAI,CAAC,CAAC;IAC5D,CAAC;CACF;AA3BD,wBA2BC;AAQD,MAAa,aAAa;IAGxB;QACE,IAAI,CAAC,UAAU,GAAG,IAAI,uBAAU,CAAC;YAC/B,OAAO,EAAE,KAAK;YACd,gBAAgB,EAAE,MAAM;YACxB,SAAS,EAAE,yCAAyC;SACrD,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,QAAsB,EACtB,MAAc;QAEd,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QAEhD,IAAI,CAAC;YACH,QAAQ,QAAQ,CAAC,MAAM,EAAE,CAAC;gBACxB,KAAK,WAAW;oBACd,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAErD,KAAK,iBAAiB;oBACpB,OAAO,MAAM,IAAI,CAAC,oBAAoB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAE3D,KAAK,cAAc;oBACjB,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAExD,KAAK,mBAAmB;oBACtB,OAAO,MAAM,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;gBAE7D;oBACE,MAAM,IAAI,KAAK,CAAC,wBAAyB,QAAgB,CAAC,MAAM,EAAE,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,OAAO,CAAC,KAAK,CAAC,uBAAuB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YACtD,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;gBAC5B,MAAM,EAAE,QAAQ,CAAC,MAAM;gBACvB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YACH,OAAO,MAAM,CAAC;QAChB,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,cAAc,CAC1B,IAAkB,EAClB,MAAc;QAEd,OAAO,CAAC,GAAG,CAAC,iBAAiB,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;QAEzC,MAAM,MAAM,GAAmB,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAC3D,IAAI,CAAC,GAAG,EACR,IAAI,CAAC,WAAW,IAAI,SAAS,CAC9B,CAAC;QAEF,IAAI,MAAM,CAAC,OAAO,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACrC,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE;gBAC7B,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,OAAO,EAAE,MAAM,CAAC,OAAO;gBACvB,QAAQ,EAAE,MAAM,CAAC,QAAQ;gBACzB,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CACT,wBAAwB,MAAM,CAAC,OAAO,CAAC,MAAM,oBAAoB,IAAI,CAAC,GAAG,EAAE,CAC5E,CAAC;QACJ,CAAC;aAAM,CAAC;YACN,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE;gBAC7B,GAAG,EAAE,IAAI,CAAC,GAAG;gBACb,KAAK,EAAE,MAAM,CAAC,YAAY;gBAC1B,WAAW,EAAE,IAAI,CAAC,WAAW;aAC9B,CAAC,CAAC;YAEH,OAAO,CAAC,KAAK,CAAC,mBAAmB,IAAI,CAAC,GAAG,KAAK,MAAM,CAAC,YAAY,EAAE,CAAC,CAAC;QACvE,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,oBAAoB,CAChC,IAAwB,EACxB,MAAc;QAEd,OAAO,CAAC,GAAG,CAAC,sCAAsC,EAAE,IAAI,CAAC,WAAW,CAAC,CAAC;QAEtE,IAAI,CAAC;YACH,MAAM,gBAAgB,GAAG,MAAM,eAAC,CAAC,iBAAiB,CAChD,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,WAAW,CACjB,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,mBAAmB,EAAE;gBACnC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBACpC,iBAAiB,EAAE,gBAAgB;gBACnC,MAAM,EAAE,IAAI,CAAC,MAAM,IAAI,MAAM;aAC9B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;QAChD,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE;gBAClC,WAAW,EAAE,IAAI,CAAC,WAAW;gBAC7B,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YAEH,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC5D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,iBAAiB,CAC7B,IAAqB,EACrB,MAAc;QAEd,OAAO,CAAC,GAAG,CAAC,cAAc,IAAI,CAAC,SAAS,OAAO,CAAC,CAAC;QAEjD,IAAI,CAAC;YACH,4CAA4C;YAC5C,MAAM,gBAAgB,GAAG,IAAI,CAAC,UAAU,CAAC,WAAW,CAClD,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,QAAQ,IAAI,SAAS,CAC3B,CAAC;YACF,MAAM,YAAY,GAAG,MAAM,eAAC,CAAC,cAAc,CACzC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,SAAS,EACd,IAAI,CAAC,QAAQ,IAAI,SAAS,CAC3B,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,gBAAgB,EAAE;gBAChC,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,QAAQ,EAAE,IAAI,CAAC,QAAQ;gBACvB,cAAc,EAAE,gBAAgB;gBAChC,UAAU,EAAE,YAAY;gBACxB,KAAK,EAAE,gBAAgB,CAAC,MAAM;aAC/B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CACT,aAAa,gBAAgB,CAAC,MAAM,kBAAkB,IAAI,CAAC,SAAS,EAAE,CACvE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,QAAQ,CAAC,kBAAkB,EAAE;gBAClC,SAAS,EAAE,IAAI,CAAC,SAAS;gBACzB,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YAEH,OAAO,CAAC,KAAK,CAAC,wBAAwB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QACzD,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,KAAK,CAAC,sBAAsB,CAClC,IAA0B,EAC1B,MAAc;QAEd,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QAEnC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,eAAC,CAAC,mBAAmB,CACzC,IAAI,CAAC,OAAO,EACZ,IAAI,CAAC,UAAU,EACf,IAAI,CAAC,UAAU,CAChB,CAAC;YAEF,MAAM,CAAC,QAAQ,CAAC,oBAAoB,EAAE;gBACpC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,MAAM;gBACpC,OAAO,EAAE,OAAO;gBAChB,UAAU,EAAE,IAAI,CAAC,UAAU;gBAC3B,UAAU,EAAE,IAAI,CAAC,UAAU;aAC5B,CAAC,CAAC;YAEH,OAAO,CAAC,GAAG,CACT,uBAAuB,IAAI,CAAC,OAAO,CAAC,MAAM,OAAO,OAAO,CAAC,MAAM,aAAa,CAC7E,CAAC;QACJ,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,MAAM,CAAC,QAAQ,CAAC,qBAAqB,EAAE;gBACrC,KAAK,EAAE,KAAK,CAAC,OAAO;aACrB,CAAC,CAAC;YAEH,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,MAAc;QAC5B,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,MAAM,aAAa,GAAG,EAAE,CAAC,CAAC,yBAAyB;QAEnD,OAAO,UAAU,GAAG,aAAa,EAAE,CAAC;YAClC,UAAU,EAAE,CAAC;YAEb,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,eAAC,CAAC,iBAAiB,CAAC,MAAM,CAAC,eAAe,EAAE,CAAC,CAAC;gBACrE,OAAO,CAAC,GAAG,CAAC,aAAa,UAAU,eAAe,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;gBAErE,MAAM,CAAC,QAAQ,CAAC,WAAW,EAAE;oBAC3B,SAAS,EAAE,UAAU;oBACrB,IAAI,EAAE,QAAQ;iBACf,CAAC,CAAC;gBAEH,QAAQ,QAAQ,CAAC,MAAM,EAAE,CAAC;oBACxB,KAAK,cAAc;wBACjB,OAAO,CAAC,GAAG,CAAC,sBAAsB,CAAC,CAAC;wBACpC,OAAO,QAAQ,CAAC,OAAO,CAAC;oBAE1B,KAAK,WAAW,CAAC;oBACjB,KAAK,iBAAiB,CAAC;oBACvB,KAAK,cAAc,CAAC;oBACpB,KAAK,mBAAmB;wBACtB,MAAM,GAAG,MAAM,IAAI,CAAC,cAAc,CAAC,QAAQ,EAAE,MAAM,CAAC,CAAC;wBACrD,MAAM;oBAER;wBACE,OAAO,CAAC,IAAI,CAAC,iBAAiB,EAAG,QAAgB,CAAC,MAAM,CAAC,CAAC;wBAC1D,OAAO,6BAA8B,QAAgB,CAAC,MAAM,EAAE,CAAC;gBACnE,CAAC;YACH,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;gBACrD,MAAM,CAAC,QAAQ,CAAC,aAAa,EAAE;oBAC7B,SAAS,EAAE,UAAU;oBACrB,KAAK,EAAE,KAAK,CAAC,OAAO;iBACrB,CAAC,CAAC;gBAEH,OAAO,mBAAmB,KAAK,CAAC,OAAO,EAAE,CAAC;YAC5C,CAAC;QACH,CAAC;QAED,OAAO,uBAAuB,aAAa,oCAAoC,CAAC;IAClF,CAAC;IAED,2CAA2C;IAC3C,gBAAgB,CAAC,MAAc;QAC7B,MAAM,WAAW,GAAG,MAAM,CAAC,eAAe,CAAC,aAAa,CAAC,CAAC;QAC1D,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,OAAO,WAAW,CAAC,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC;QAC1D,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0CAA0C;IAC1C,yBAAyB,CAAC,MAAc;QACtC,MAAM,aAAa,GAAG,MAAM,CAAC,eAAe,CAAC,mBAAmB,CAAC,CAAC;QAClE,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,OAAO,aAAa,CAAC,aAAa,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,IAAI,CAAC,iBAAiB,CAAC;QACxE,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;CACF;AA1PD,sCA0PC"}