/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
import type { BamlRuntime, BamlCtxManager, ClientRegistry } from "@boundaryml/baml";
import { HTTPRequest } from "@boundaryml/baml";
import type TypeBuilder from "./type_builder";
type BamlCallOptions = {
    tb?: TypeBuilder;
    clientRegistry?: ClientRegistry;
    env?: Record<string, string | undefined>;
};
export declare class HttpRequest {
    private runtime;
    private ctxManager;
    constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager);
    DetermineNextStep(thread: string, __baml_options__?: BamlCallOptions): HTTPRequest;
    ExtractWebData(content: string, data_type: string, selector?: string | null, __baml_options__?: BamlCallOptions): HTTPRequest;
    ProcessWebContent(content: string, instruction: string, __baml_options__?: BamlCallOptions): HTTPRequest;
    SummarizeWebContent(content: string, max_length?: number | null, focus_area?: string | null, __baml_options__?: BamlCallOptions): HTTPRequest;
}
export declare class HttpStreamRequest {
    private runtime;
    private ctxManager;
    constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager);
    DetermineNextStep(thread: string, __baml_options__?: BamlCallOptions): HTTPRequest;
    ExtractWebData(content: string, data_type: string, selector?: string | null, __baml_options__?: BamlCallOptions): HTTPRequest;
    ProcessWebContent(content: string, instruction: string, __baml_options__?: BamlCallOptions): HTTPRequest;
    SummarizeWebContent(content: string, max_length?: number | null, focus_area?: string | null, __baml_options__?: BamlCallOptions): HTTPRequest;
}
export {};
//# sourceMappingURL=sync_request.d.ts.map