"use strict";
/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
Object.defineProperty(exports, "__esModule", { value: true });
exports.resetBamlEnvVars = exports.setLogJsonMode = exports.getLogLevel = exports.setLogLevel = void 0;
// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
var logging_1 = require("@boundaryml/baml/logging");
Object.defineProperty(exports, "setLogLevel", { enumerable: true, get: function () { return logging_1.setLogLevel; } });
Object.defineProperty(exports, "getLogLevel", { enumerable: true, get: function () { return logging_1.getLogLevel; } });
Object.defineProperty(exports, "setLogJsonMode", { enumerable: true, get: function () { return logging_1.setLogJsonMode; } });
var globals_1 = require("./globals");
Object.defineProperty(exports, "resetBamlEnvVars", { enumerable: true, get: function () { return globals_1.resetBamlEnvVars; } });
//# sourceMappingURL=config.js.map