"use strict";
/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
Object.defineProperty(exports, "__esModule", { value: true });
exports.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX = exports.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME = void 0;
exports.resetBamlEnvVars = resetBamlEnvVars;
// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
const baml_1 = require("@boundaryml/baml");
const inlinedbaml_1 = require("./inlinedbaml");
// Create a copy of process.env to avoid mutations
const env = { ...process.env };
exports.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME = baml_1.BamlRuntime.fromFiles('baml_src', (0, inlinedbaml_1.getBamlFiles)(), env);
exports.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX = new baml_1.BamlCtxManager(exports.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME);
/**
 * @deprecated resetBamlEnvVars is deprecated and is safe to remove, since environment variables are now lazily loaded on each function call
 */
function resetBamlEnvVars(envVars) {
    if (exports.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX.allowResets()) {
        const envVarsToReset = Object.fromEntries(Object.entries(envVars).filter((kv) => kv[1] !== undefined));
        exports.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME.reset('baml_src', (0, inlinedbaml_1.getBamlFiles)(), envVarsToReset);
        exports.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX.reset();
    }
    else {
        throw new Error('BamlError: Cannot reset BAML environment variables while there are active BAML contexts.');
    }
}
//# sourceMappingURL=globals.js.map