"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebFetcher = void 0;
const node_fetch_1 = __importDefault(require("node-fetch"));
const html_to_text_1 = require("html-to-text");
const cheerio = __importStar(require("cheerio"));
const url_1 = require("url");
class WebFetcher {
    constructor(options = {}) {
        this.options = options;
        this.defaultOptions = {
            timeout: 10000,
            maxContentLength: 100000,
            userAgent: 'WebFetch-Tool/1.0.0',
            followRedirects: true
        };
        this.options = { ...this.defaultOptions, ...options };
    }
    async fetchUrl(url, description) {
        try {
            // Validate URL
            const parsedUrl = new url_1.URL(url);
            if (!['http:', 'https:'].includes(parsedUrl.protocol)) {
                throw new Error('Only HTTP and HTTPS URLs are supported');
            }
            console.log(`Fetching URL: ${url}${description ? ` (${description})` : ''}`);
            // Create abort controller for timeout
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.options.timeout);
            try {
                const response = await (0, node_fetch_1.default)(url, {
                    signal: controller.signal,
                    headers: {
                        'User-Agent': this.options.userAgent,
                        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                        'Accept-Language': 'en-US,en;q=0.5',
                        'Accept-Encoding': 'gzip, deflate',
                        'Connection': 'keep-alive',
                        'Upgrade-Insecure-Requests': '1',
                    },
                    redirect: this.options.followRedirects ? 'follow' : 'manual'
                });
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                const contentType = response.headers.get('content-type') || '';
                const contentLength = parseInt(response.headers.get('content-length') || '0');
                // Check content length
                if (contentLength > this.options.maxContentLength) {
                    throw new Error(`Content too large: ${contentLength} bytes (max: ${this.options.maxContentLength})`);
                }
                const rawContent = await response.text();
                // Limit content length
                const content = rawContent.length > this.options.maxContentLength
                    ? rawContent.substring(0, this.options.maxContentLength) + '...[truncated]'
                    : rawContent;
                // Extract metadata
                const metadata = {
                    url,
                    contentType,
                    contentLength: content.length,
                    lastModified: response.headers.get('last-modified') || undefined,
                    statusCode: response.status
                };
                // Extract title and description from HTML
                if (contentType.includes('text/html')) {
                    const $ = cheerio.load(content);
                    metadata.title = $('title').first().text().trim() || undefined;
                    metadata.description = $('meta[name="description"]').attr('content') ||
                        $('meta[property="og:description"]').attr('content') || undefined;
                }
                return {
                    success: true,
                    content,
                    metadata
                };
            }
            finally {
                clearTimeout(timeoutId);
            }
        }
        catch (error) {
            console.error(`Error fetching ${url}:`, error.message);
            return {
                success: false,
                errorMessage: error.message || 'Unknown error occurred',
                metadata: {
                    url,
                    statusCode: 0
                }
            };
        }
    }
    convertHtmlToText(html) {
        return (0, html_to_text_1.convert)(html, {
            wordwrap: false,
            selectors: [
                { selector: 'a', options: { ignoreHref: true } },
                { selector: 'img', format: 'skip' },
                { selector: 'script', format: 'skip' },
                { selector: 'style', format: 'skip' },
                { selector: 'nav', format: 'skip' },
                { selector: 'footer', format: 'skip' }
            ]
        });
    }
    extractData(content, dataType, selector) {
        const $ = cheerio.load(content);
        const results = [];
        switch (dataType.toLowerCase()) {
            case 'links':
                $('a[href]').each((_, element) => {
                    const href = $(element).attr('href');
                    const text = $(element).text().trim();
                    if (href) {
                        results.push(`${text || href}: ${href}`);
                    }
                });
                break;
            case 'images':
                $('img[src]').each((_, element) => {
                    const src = $(element).attr('src');
                    const alt = $(element).attr('alt') || '';
                    if (src) {
                        results.push(`${alt}: ${src}`);
                    }
                });
                break;
            case 'text':
                if (selector) {
                    $(selector).each((_, element) => {
                        const text = $(element).text().trim();
                        if (text)
                            results.push(text);
                    });
                }
                else {
                    results.push(this.convertHtmlToText(content));
                }
                break;
            case 'tables':
                $('table').each((_, table) => {
                    const tableData = [];
                    $(table).find('tr').each((_, row) => {
                        const rowData = [];
                        $(row).find('td, th').each((_, cell) => {
                            rowData.push($(cell).text().trim());
                        });
                        if (rowData.length > 0) {
                            tableData.push(rowData.join(' | '));
                        }
                    });
                    if (tableData.length > 0) {
                        results.push(tableData.join('\n'));
                    }
                });
                break;
            case 'metadata':
                $('meta').each((_, element) => {
                    const name = $(element).attr('name') || $(element).attr('property');
                    const content = $(element).attr('content');
                    if (name && content) {
                        results.push(`${name}: ${content}`);
                    }
                });
                break;
            default:
                if (selector) {
                    $(selector).each((_, element) => {
                        const text = $(element).text().trim();
                        if (text)
                            results.push(text);
                    });
                }
                break;
        }
        return results;
    }
    isPrivateIp(url) {
        try {
            const hostname = new url_1.URL(url).hostname;
            const privateRanges = [
                /^10\./,
                /^127\./,
                /^172\.(1[6-9]|2[0-9]|3[0-1])\./,
                /^192\.168\./,
                /^::1$/,
                /^fc00:/,
                /^fe80:/,
                /^localhost$/i
            ];
            return privateRanges.some(range => range.test(hostname));
        }
        catch {
            return false;
        }
    }
}
exports.WebFetcher = WebFetcher;
//# sourceMappingURL=webFetcher.js.map