{"version": 3, "file": "cli.js", "sourceRoot": "", "sources": ["../../src/cli.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,mDAAqC;AACrC,mCAAgD;AAEhD,MAAM,WAAW;IAIf;QACE,IAAI,CAAC,KAAK,GAAG,IAAI,qBAAa,EAAE,CAAC;QACjC,IAAI,CAAC,EAAE,GAAG,QAAQ,CAAC,eAAe,CAAC;YACjC,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,MAAM,EAAE,OAAO,CAAC,MAAM;SACvB,CAAC,CAAC;IACL,CAAC;IAEO,cAAc;QACpB,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,qBAAqB,CAAC,CAAC;QACnC,OAAO,CAAC,GAAG,CAAC,2DAA2D,CAAC,CAAC;QACzE,OAAO,CAAC,GAAG,CACT,iEAAiE,CAClE,CAAC;QACF,OAAO,CAAC,GAAG,CACT,yFAAyF,CAC1F,CAAC;QACF,OAAO,CAAC,GAAG,CAAC,sDAAsD,CAAC,CAAC;QACpE,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;QACjE,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;QAChE,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5B,OAAO,CAAC,GAAG,CAAC,uDAAuD,CAAC,CAAC;IACvE,CAAC;IAEO,YAAY,CAAC,KAAa;QAChC,MAAM,KAAK,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;QACxC,MAAM,OAAO,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;QACvC,MAAM,IAAI,GAAG,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;QAC5B,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,CAAC;IAC3B,CAAC;IAEO,KAAK,CAAC,mBAAmB,CAC/B,OAAe,EACf,IAAc,EACd,MAAc,EACd,QAAgB,EAAE;QAElB,QAAQ,OAAO,EAAE,CAAC;YAChB,KAAK,OAAO;gBACV,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtB,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;oBAC/C,OAAO,MAAM,CAAC;gBAChB,CAAC;gBACD,MAAM,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACpB,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC;gBAEzD,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;oBAC5B,OAAO,EAAE,WAAW;oBACpB,GAAG;oBACH,WAAW;iBACZ,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,SAAS;gBACZ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtB,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;oBACxD,OAAO,MAAM,CAAC;gBAChB,CAAC;gBACD,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACnC,MAAM,OAAO,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAEpD,IAAI,CAAC,OAAO,EAAE,CAAC;oBACb,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;oBACjE,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAED,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;oBAC5B,OAAO,EAAE,iBAAiB;oBAC1B,OAAO;oBACP,WAAW;iBACZ,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,SAAS;gBACZ,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;oBACtB,OAAO,CAAC,GAAG,CACT,+EAA+E,CAChF,CAAC;oBACF,OAAO,MAAM,CAAC;gBAChB,CAAC;gBACD,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC;gBACzB,MAAM,QAAQ,GAAG,IAAI,CAAC,CAAC,CAAC,IAAI,SAAS,CAAC;gBACtC,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAE3D,IAAI,CAAC,cAAc,EAAE,CAAC;oBACpB,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;oBACjE,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAED,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;oBAC5B,OAAO,EAAE,cAAc;oBACvB,OAAO,EAAE,cAAc;oBACvB,SAAS,EAAE,QAAQ;oBACnB,QAAQ;iBACT,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,WAAW;gBACd,MAAM,SAAS,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;gBAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,SAAS,CAAC;gBACvD,MAAM,gBAAgB,GAAG,IAAI,CAAC,KAAK,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC;gBAE7D,IAAI,CAAC,gBAAgB,EAAE,CAAC;oBACtB,OAAO,CAAC,GAAG,CAAC,mDAAmD,CAAC,CAAC;oBACjE,OAAO,MAAM,CAAC;gBAChB,CAAC;gBAED,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;oBAC5B,OAAO,EAAE,mBAAmB;oBAC5B,OAAO,EAAE,gBAAgB;oBACzB,UAAU,EAAE,SAAS;oBACrB,UAAU,EAAE,SAAS;iBACtB,CAAC,CAAC;gBACH,MAAM;YAER,KAAK,MAAM;gBACT,IAAI,CAAC,cAAc,EAAE,CAAC;gBACtB,OAAO,MAAM,CAAC;YAEhB;gBACE,kCAAkC;gBAClC,MAAM,CAAC,QAAQ,CAAC,YAAY,EAAE;oBAC5B,IAAI,EAAE,KAAK;iBACZ,CAAC,CAAC;gBACH,MAAM;QACV,CAAC;QAED,OAAO,MAAM,CAAC;IAChB,CAAC;IAEO,cAAc,CAAC,MAAc;QACnC,MAAM,SAAS,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;QAExC,IAAI,CAAC,SAAS;YAAE,OAAO;QAEvB,QAAQ,SAAS,CAAC,IAAI,EAAE,CAAC;YACvB,KAAK,aAAa;gBAChB,OAAO,CAAC,GAAG,CAAC,8BAA8B,CAAC,CAAC;gBAC5C,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,KAAK,IAAI,KAAK,EAAE,CAAC,CAAC;gBACpE,OAAO,CAAC,GAAG,CACT,sBAAsB,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,MAAM,aAAa,CACjE,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,WAAW,SAAS,CAAC,IAAI,CAAC,GAAG,EAAE,CAAC,CAAC;gBAC7C,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ,EAAE,WAAW,EAAE,CAAC;oBACzC,OAAO,CAAC,GAAG,CAAC,mBAAmB,SAAS,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,EAAE,CAAC,CAAC;gBACxE,CAAC;gBACD,MAAM;YAER,KAAK,mBAAmB;gBACtB,OAAO,CAAC,GAAG,CAAC,uBAAuB,CAAC,CAAC;gBACrC,OAAO,CAAC,GAAG,CAAC,mBAAmB,SAAS,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,CAAC;gBAC7D,OAAO,CAAC,GAAG,CAAC,eAAe,SAAS,CAAC,IAAI,CAAC,iBAAiB,EAAE,CAAC,CAAC;gBAC/D,MAAM;YAER,KAAK,gBAAgB;gBACnB,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;gBAClC,OAAO,CAAC,GAAG,CAAC,YAAY,SAAS,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;gBACpD,OAAO,CAAC,GAAG,CAAC,aAAa,SAAS,CAAC,IAAI,CAAC,KAAK,QAAQ,CAAC,CAAC;gBACvD,IAAI,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBAC7C,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;oBAC3B,SAAS,CAAC,IAAI,CAAC,cAAc;yBAC1B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;yBACZ,OAAO,CAAC,CAAC,IAAY,EAAE,KAAa,EAAE,EAAE;wBACvC,OAAO,CAAC,GAAG,CAAC,KAAK,KAAK,GAAG,CAAC,KAAK,IAAI,EAAE,CAAC,CAAC;oBACzC,CAAC,CAAC,CAAC;oBACL,IAAI,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EAAE,EAAE,CAAC;wBAC9C,OAAO,CAAC,GAAG,CACT,aACE,SAAS,CAAC,IAAI,CAAC,cAAc,CAAC,MAAM,GAAG,EACzC,aAAa,CACd,CAAC;oBACJ,CAAC;gBACH,CAAC;gBACD,MAAM;YAER,KAAK,oBAAoB;gBACvB,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,CAAC;gBACtC,OAAO,CAAC,GAAG,CACT,gBAAgB,SAAS,CAAC,IAAI,CAAC,eAAe,aAAa,CAC5D,CAAC;gBACF,OAAO,CAAC,GAAG,CAAC,gBAAgB,SAAS,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC,CAAC;gBACtD,MAAM;YAER,KAAK,aAAa,CAAC;YACnB,KAAK,kBAAkB,CAAC;YACxB,KAAK,kBAAkB,CAAC;YACxB,KAAK,qBAAqB,CAAC;YAC3B,KAAK,YAAY,CAAC;YAClB,KAAK,aAAa;gBAChB,OAAO,CAAC,GAAG,CAAC,cAAc,SAAS,CAAC,IAAI,CAAC,KAAK,EAAE,CAAC,CAAC;gBAClD,MAAM;QACV,CAAC;IACH,CAAC;IAED,KAAK,CAAC,GAAG;QACP,IAAI,CAAC,cAAc,EAAE,CAAC;QAEtB,MAAM,MAAM,GAAG,IAAI,cAAM,EAAE,CAAC;QAE5B,OAAO,IAAI,EAAE,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,KAAK,GAAG,MAAM,IAAI,OAAO,CAAS,CAAC,OAAO,EAAE,EAAE;oBAClD,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAC;gBAC9C,CAAC,CAAC,CAAC;gBAEH,IAAI,KAAK,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,KAAK,MAAM,EAAE,CAAC;oBAC1C,OAAO,CAAC,GAAG,CAAC,aAAa,CAAC,CAAC;oBAC3B,MAAM;gBACR,CAAC;gBAED,IAAI,KAAK,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE,CAAC;oBACxB,SAAS;gBACX,CAAC;gBAED,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,IAAI,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;gBAEnD,6CAA6C;gBAC7C,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,mBAAmB,CAClD,OAAO,EACP,IAAI,EACJ,MAAM,EACN,KAAK,CACN,CAAC;gBAEF,sCAAsC;gBACtC,IAAI,aAAa,CAAC,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,EAAE,CAAC;oBACvD,OAAO,CAAC,GAAG,CAAC,oBAAoB,CAAC,CAAC;oBAClC,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;oBAEzD,kBAAkB;oBAClB,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC;oBAEnC,oCAAoC;oBACpC,IAAI,MAAM,IAAI,MAAM,CAAC,IAAI,EAAE,EAAE,CAAC;wBAC5B,OAAO,CAAC,GAAG,CAAC,eAAe,MAAM,EAAE,CAAC,CAAC;oBACvC,CAAC;gBACH,CAAC;YACH,CAAC;YAAC,OAAO,KAAU,EAAE,CAAC;gBACpB,OAAO,CAAC,KAAK,CAAC,YAAY,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAC;IAClB,CAAC;CACF;AAQQ,kCAAW;AANpB,4CAA4C;AAC5C,IAAI,OAAO,CAAC,IAAI,KAAK,MAAM,EAAE,CAAC;IAC5B,MAAM,GAAG,GAAG,IAAI,WAAW,EAAE,CAAC;IAC9B,GAAG,CAAC,GAAG,EAAE,CAAC,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;AACjC,CAAC"}