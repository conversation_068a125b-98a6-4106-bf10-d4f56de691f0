/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
import { FieldType } from '@boundaryml/baml/native';
import { EnumBuilder, ClassBuilder, ClassViewer } from '@boundaryml/baml/type_builder';
export default class TypeBuilder {
    private tb;
    DoneForNow: ClassViewer<'DoneForNow', "intent" | "message">;
    ExtractDataTool: ClassViewer<'ExtractDataTool', "intent" | "content" | "data_type" | "selector">;
    FetchUrlTool: ClassViewer<'FetchUrlTool', "intent" | "url" | "description">;
    ProcessContentTool: ClassViewer<'ProcessContentTool', "intent" | "content" | "instruction" | "format">;
    SummarizeContentTool: ClassViewer<'SummarizeContentTool', "intent" | "content" | "max_length" | "focus_area">;
    WebFetchResult: ClassViewer<'WebFetchResult', "success" | "content" | "error_message" | "metadata">;
    WebMetadata: ClassViewer<'WebMetadata', "title" | "description" | "url" | "content_type" | "content_length" | "last_modified" | "status_code">;
    constructor();
    __tb(): import("@boundaryml/baml/native").TypeBuilder;
    string(): FieldType;
    literalString(value: string): FieldType;
    literalInt(value: number): FieldType;
    literalBool(value: boolean): FieldType;
    int(): FieldType;
    float(): FieldType;
    bool(): FieldType;
    list(type: FieldType): FieldType;
    null(): FieldType;
    map(key: FieldType, value: FieldType): FieldType;
    union(types: FieldType[]): FieldType;
    addClass<Name extends string>(name: Name): ClassBuilder<Name>;
    addEnum<Name extends string>(name: Name): EnumBuilder<Name>;
    addBaml(baml: string): void;
}
//# sourceMappingURL=type_builder.d.ts.map