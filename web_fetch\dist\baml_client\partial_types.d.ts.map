{"version": 3, "file": "partial_types.d.ts", "sourceRoot": "", "sources": ["../../baml_client/partial_types.ts"], "names": [], "mappings": "AAAA;;;;;;;;kGAQkG;AAiBlG;;;;;+EAK+E;AAE/E,MAAM,WAAW,WAAW,CAAC,CAAC;IAC5B,KAAK,EAAE,CAAC,CAAA;IACR,KAAK,EAAE,SAAS,GAAG,YAAY,GAAG,UAAU,CAAA;CAC7C;AAED,yBAAiB,aAAa,CAAC;IAC3B,UAAiB,UAAU;QACzB,MAAM,CAAC,EAAE,cAAc,GAAG,IAAI,CAAA;QAC9B,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;KACxB;IACD,UAAiB,eAAe;QAC9B,MAAM,CAAC,EAAE,cAAc,GAAG,IAAI,CAAA;QAC9B,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACvB,SAAS,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACzB,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;KACzB;IACD,UAAiB,YAAY;QAC3B,MAAM,CAAC,EAAE,WAAW,GAAG,IAAI,CAAA;QAC3B,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACnB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;KAC5B;IACD,UAAiB,kBAAkB;QACjC,MAAM,CAAC,EAAE,iBAAiB,GAAG,IAAI,CAAA;QACjC,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACvB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QAC3B,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;KACvB;IACD,UAAiB,oBAAoB;QACnC,MAAM,CAAC,EAAE,mBAAmB,GAAG,IAAI,CAAA;QACnC,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACvB,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QAC1B,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;KAC3B;IACD,UAAiB,cAAc;QAC7B,OAAO,CAAC,EAAE,OAAO,GAAG,IAAI,CAAA;QACxB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACvB,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QAC7B,QAAQ,CAAC,EAAE,WAAW,GAAG,IAAI,CAAA;KAC9B;IACD,UAAiB,WAAW;QAC1B,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACrB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QAC3B,GAAG,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QACnB,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QAC5B,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QAC9B,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;QAC7B,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;KAC5B;IACL,KAAY,aAAa,GAAG,YAAY,GAAG,kBAAkB,GAAG,eAAe,GAAG,oBAAoB,GAAG,IAAI,CAAA;CAE5G"}