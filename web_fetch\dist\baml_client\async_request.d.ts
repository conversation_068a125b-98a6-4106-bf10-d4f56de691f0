/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
import type { BamlRuntime, BamlCtxManager, ClientRegistry } from "@boundaryml/baml";
import { HTTPRequest } from "@boundaryml/baml";
import type TypeBuilder from "./type_builder";
type BamlCallOptions = {
    tb?: TypeBuilder;
    clientRegistry?: ClientRegistry;
    env?: Record<string, string | undefined>;
};
export declare class AsyncHttpRequest {
    private runtime;
    private ctxManager;
    constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager);
    DetermineNextStep(thread: string, __baml_options__?: BamlCallOptions): Promise<HTTPRequest>;
    ExtractWebData(content: string, data_type: string, selector?: string | null, __baml_options__?: BamlCallOptions): Promise<HTTPRequest>;
    ProcessWebContent(content: string, instruction: string, __baml_options__?: BamlCallOptions): Promise<HTTPRequest>;
    SummarizeWebContent(content: string, max_length?: number | null, focus_area?: string | null, __baml_options__?: BamlCallOptions): Promise<HTTPRequest>;
}
export declare class AsyncHttpStreamRequest {
    private runtime;
    private ctxManager;
    constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager);
    DetermineNextStep(thread: string, __baml_options__?: BamlCallOptions): Promise<HTTPRequest>;
    ExtractWebData(content: string, data_type: string, selector?: string | null, __baml_options__?: BamlCallOptions): Promise<HTTPRequest>;
    ProcessWebContent(content: string, instruction: string, __baml_options__?: BamlCallOptions): Promise<HTTPRequest>;
    SummarizeWebContent(content: string, max_length?: number | null, focus_area?: string | null, __baml_options__?: BamlCallOptions): Promise<HTTPRequest>;
}
export {};
//# sourceMappingURL=async_request.d.ts.map