{"name": "web-fetch-tool", "version": "1.0.0", "description": "Web Fetch Tool implementation using BAML framework, inspired by Gemini CLI", "main": "src/index.ts", "scripts": {"build": "tsc", "dev": "ts-node src/index.ts", "cli": "ts-node src/cli.ts", "generate": "npx baml-cli generate", "test": "npx baml-cli test"}, "bin": {"web-fetch": "./dist/src/cli.js"}, "keywords": ["baml", "web-fetch", "ai", "gemini-cli", "12-factor-agents", "web-scraping"], "author": "Your Name", "license": "MIT", "dependencies": {"@boundaryml/baml": "^0.202.1", "node-fetch": "^3.3.2", "html-to-text": "^9.0.5", "cheerio": "^1.0.0-rc.12"}, "devDependencies": {"@types/node": "^20.0.0", "@types/html-to-text": "^9.0.4", "typescript": "^5.0.0", "ts-node": "^10.9.0"}}