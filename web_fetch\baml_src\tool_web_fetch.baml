// Web Fetch Tool definitions
type WebFetchTools = FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool

class FetchUrlTool {
    intent "fetch_url"
    url string
    description string?
}

class ProcessContentTool {
    intent "process_content"
    content string
    instruction string
    format string? // "text" | "json" | "markdown"
}

class ExtractDataTool {
    intent "extract_data"
    content string
    data_type string // "links" | "images" | "text" | "tables" | "metadata"
    selector string? // CSS selector for specific extraction
}

class SummarizeContentTool {
    intent "summarize_content"
    content string
    max_length int? // Maximum length of summary
    focus_area string? // Specific area to focus on
}

// Response types
class WebFetchResult {
    success bool
    content string?
    error_message string?
    metadata WebMetadata?
}

class WebMetadata {
    title string?
    description string?
    url string
    content_type string?
    content_length int?
    last_modified string?
    status_code int?
}
