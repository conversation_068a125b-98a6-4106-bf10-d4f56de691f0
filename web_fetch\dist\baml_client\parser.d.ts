/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
import type { BamlRuntime, BamlCtxManager, ClientRegistry } from "@boundaryml/baml";
import type { DoneForNow, ExtractDataTool, FetchUrlTool, ProcessContentTool, SummarizeContentTool } from "./types";
import type TypeBuilder from "./type_builder";
export declare class LlmResponseParser {
    private runtime;
    private ctxManager;
    constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager);
    DetermineNextStep(llmResponse: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        env?: Record<string, string | undefined>;
    }): FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool | DoneForNow;
    ExtractWebData(llmResponse: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        env?: Record<string, string | undefined>;
    }): string;
    ProcessWebContent(llmResponse: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        env?: Record<string, string | undefined>;
    }): string;
    SummarizeWebContent(llmResponse: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        env?: Record<string, string | undefined>;
    }): string;
}
export declare class LlmStreamParser {
    private runtime;
    private ctxManager;
    constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager);
    DetermineNextStep(llmResponse: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        env?: Record<string, string | undefined>;
    }): FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool | DoneForNow;
    ExtractWebData(llmResponse: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        env?: Record<string, string | undefined>;
    }): string;
    ProcessWebContent(llmResponse: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        env?: Record<string, string | undefined>;
    }): string;
    SummarizeWebContent(llmResponse: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        env?: Record<string, string | undefined>;
    }): string;
}
//# sourceMappingURL=parser.d.ts.map