/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

import type { Image, Audio, Pdf, Video } from "@boundaryml/baml"
import type { Checked, Check } from "./types"
import type {  ClarificationRequest,  ClearMemoryTool,  DoneForNow,  RecallMemoryTool,  SaveMemoryTool } from "./types"
import type * as types from "./types"

/******************************************************************************
*
*  These types are used for streaming, for when an instance of a type
*  is still being built up and any of its fields is not yet fully available.
*
******************************************************************************/

export interface StreamState<T> {
  value: T
  state: "Pending" | "Incomplete" | "Complete"
}

export namespace partial_types {
    export interface ClarificationRequest {
      intent?: "request_more_information" | null
      message?: string | null
    }
    export interface ClearMemoryTool {
      intent?: "clear_memory" | null
      pattern?: string | null
    }
    export interface DoneForNow {
      intent?: "done_for_now" | null
      message?: string | null
    }
    export interface RecallMemoryTool {
      intent?: "recall_memory" | null
      query?: string | null
    }
    export interface SaveMemoryTool {
      intent?: "save_memory" | null
      fact?: string | null
    }
export type AllTools = SaveMemoryTool | RecallMemoryTool | ClearMemoryTool | DoneForNow | ClarificationRequest | null

export type MemoryTools = SaveMemoryTool | RecallMemoryTool | ClearMemoryTool | null

}