"use strict";
/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.BamlClientFinishReasonError = exports.BamlValidationError = exports.BamlClientHttpError = exports.resetBamlEnvVars = exports.b = exports.version = void 0;
// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
/**
 * If this import fails, you may need to upgrade @boundaryml/baml.
 *
 * Please upgrade @boundaryml/baml to 0.202.1.
 *
 * $ npm install @boundaryml/baml@0.202.1
 * $ yarn add @boundaryml/baml@0.202.1
 * $ pnpm add @boundaryml/baml@0.202.1
 *
 * If nothing else works, please ask for help:
 *
 * https://github.com/boundaryml/baml/issues
 * https://boundaryml.com/discord
 *
 **/
const baml_1 = require("@boundaryml/baml");
exports.version = "0.202.1";
(0, baml_1.ThrowIfVersionMismatch)(exports.version);
var async_client_1 = require("./async_client");
Object.defineProperty(exports, "b", { enumerable: true, get: function () { return async_client_1.b; } });
__exportStar(require("./types"), exports);
__exportStar(require("./tracing"), exports);
var globals_1 = require("./globals");
Object.defineProperty(exports, "resetBamlEnvVars", { enumerable: true, get: function () { return globals_1.resetBamlEnvVars; } });
var baml_2 = require("@boundaryml/baml");
Object.defineProperty(exports, "BamlClientHttpError", { enumerable: true, get: function () { return baml_2.BamlClientHttpError; } });
Object.defineProperty(exports, "BamlValidationError", { enumerable: true, get: function () { return baml_2.BamlValidationError; } });
Object.defineProperty(exports, "BamlClientFinishReasonError", { enumerable: true, get: function () { return baml_2.BamlClientFinishReasonError; } });
//# sourceMappingURL=index.js.map