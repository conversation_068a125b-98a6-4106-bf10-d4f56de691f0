/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

const fileMap = {
  
  "agent.baml": "// Memory Agent - 内存代理\n// 处理内存相关的操作和用户交互\n\nclass DoneForNow {\n    intent \"done_for_now\"\n    message string @description(\"发送给用户的关于已完成工作的消息\")\n}\n\nclass ClarificationRequest {\n    intent \"request_more_information\" @description(\"请求用户提供更多信息\")\n    message string @description(\"向用户请求澄清的消息\")\n}\n\n// 所有可用的工具类型\ntype AllTools = MemoryTools | DoneForNow | ClarificationRequest\n\nfunction DetermineNextStep(\n    thread: string,\n    memory_context: string?\n) -> AllTools {\n    client DouBao\n\n    prompt #\"\n        {{ _.role(\"system\") }}\n        你是一个有用的助手，专门处理内存管理任务。你可以帮助用户保存、回忆和管理信息。\n\n        你有以下工具可用：\n        1. save_memory - 保存一个事实或信息到内存中\n        2. recall_memory - 回忆之前保存的内存信息\n        3. clear_memory - 清除内存信息\n        4. request_more_information - 请求用户提供更多信息\n        5. done_for_now - 完成当前任务\n\n        重要规则：\n        - 在保存新信息前，请仔细检查现有内存是否已包含相似信息\n        - 如果用户询问问题（如\"我喜欢喝什么\"），应该使用 recall_memory 而不是 save_memory\n        - 避免保存重复或相似的信息\n        - 优先使用现有信息回答用户问题\n\n        {% if memory_context %}\n        当前内存上下文：\n        {{ memory_context }}\n        {% endif %}\n\n        {{ _.role(\"user\") }}\n        你正在处理以下对话线程：\n\n        {{ thread }}\n\n        基于用户的请求和现有内存上下文，确定下一步应该做什么。\n\n        {{ ctx.output_format }}\n    \"#\n}\n\n// 测试用例\ntest SaveMemoryBasic {\n    functions [DetermineNextStep]\n    args {\n        thread #\"\n            <user_input>\n            我喜欢喝咖啡\n            </user_input>\n        \"#\n        memory_context null\n    }\n    @@assert(intent, {{this.intent == \"save_memory\"}})\n    @@assert(fact_content, {{\"咖啡\" in this.fact}})\n}\n\ntest SaveMemoryWithDetails {\n    functions [DetermineNextStep]\n    args {\n        thread #\"\n            <user_input>\n            请记住我的首选编程语言是 Python，我正在开发一个名为 'gemini-cli-tools' 的项目\n            </user_input>\n        \"#\n        memory_context null\n    }\n    @@assert(intent, {{this.intent == \"save_memory\"}})\n    @@assert(fact_content, {{\"Python\" in this.fact and \"gemini-cli\" in this.fact}})\n}\n\ntest RecallMemory {\n    functions [DetermineNextStep]\n    args {\n        thread #\"\n            <user_input>\n            你还记得我之前告诉你的关于编程语言的事情吗？\n            </user_input>\n        \"#\n        memory_context \"## Gemini Added Memories\\n- 我首选的编程语言是 Python\\n- 我正在开发一个名为 'gemini-cli-tools' 的项目\"\n    }\n    @@assert(intent, {{this.intent == \"recall_memory\"}})\n}\n\ntest RecallSpecificMemory {\n    functions [DetermineNextStep]\n    args {\n        thread #\"\n            <user_input>\n            我的项目名称是什么？\n            </user_input>\n        \"#\n        memory_context \"## Gemini Added Memories\\n- 我首选的编程语言是 Python\\n- 我正在开发一个名为 'gemini-cli' 的项目\"\n    }\n    @@assert(intent, {{this.intent == \"recall_memory\"}})\n    @@assert(query_content, {{\"项目\" in this.query}})\n}\n\ntest ClearMemory {\n    functions [DetermineNextStep]\n    args {\n        thread #\"\n            <user_input>\n            请清除所有内存\n            </user_input>\n        \"#\n        memory_context \"## Gemini Added Memories\\n- 我首选的编程语言是 Python\"\n    }\n    @@assert(intent, {{this.intent == \"clear_memory\"}})\n}\n\ntest RequestClarification {\n    functions [DetermineNextStep]\n    args {\n        thread #\"\n            <user_input>\n            记住那个东西\n            </user_input>\n        \"#\n        memory_context null\n    }\n    @@assert(intent, {{this.intent == \"request_more_information\"}})\n}\n\ntest CompleteTask {\n    functions [DetermineNextStep]\n    args {\n        thread #\"\n            <user_input>\n            我喜欢喝咖啡\n            </user_input>\n\n            <save_memory>\n            fact: 用户喜欢喝咖啡\n            </save_memory>\n\n            <tool_response>\n            已成功保存到内存：用户喜欢喝咖啡\n            </tool_response>\n        \"#\n        memory_context null\n    }\n    @@assert(intent, {{this.intent == \"done_for_now\"}})\n    @@assert(message_content, {{\"保存\" in this.message or \"记住\" in this.message}})\n}\n",
  "clients.baml": "// Learn more about clients at https://docs.boundaryml.com/docs/snippets/clients/overview\n\nclient<llm> DouBao {\n  provider openai\n  options {\n    model \"doubao-seed-1.6\"\n    api_key \"974fd8d1c155aa3d04b17bf253176b5e\"\n    base_url \"https://gateway.chat.sensedeal.vip/v1\"\n  }\n}\n\nclient<llm> Qwen2_5_32B {\n  provider openai\n  options {\n    model \"qwen2.5-32b-instruct-int4\"\n    api_key \"974fd8d1c155aa3d04b17bf253176b5e\"\n    base_url \"https://gateway.chat.sensedeal.vip/v1\"\n  }\n}\n\nclient<llm> CustomGPT4o {\n  provider openai\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n  }\n}\n\nclient<llm> CustomGPT4oMini {\n  provider openai\n  retry_policy Exponential\n  options {\n    model \"gpt-4o-mini\"\n    api_key env.OPENAI_API_KEY\n  }\n}\n\nclient<llm> CustomSonnet {\n  provider anthropic\n  options {\n    model \"claude-3-5-sonnet-20241022\"\n    api_key env.ANTHROPIC_API_KEY\n  }\n}\n\nclient<llm> CustomHaiku {\n  provider anthropic\n  retry_policy Constant\n  options {\n    model \"claude-3-haiku-20240307\"\n    api_key env.ANTHROPIC_API_KEY\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/round-robin\nclient<llm> CustomFast {\n  provider round-robin\n  options {\n    // This will alternate between the clients, DouBao first\n    strategy [DouBao, Qwen2_5_32B]\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/fallback\nclient<llm> MemoryFallback {\n  provider fallback\n  options {\n    // This will try the clients in order until one succeeds, DouBao first\n    strategy [DouBao, Qwen2_5_32B, CustomGPT4oMini, CustomHaiku]\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/retry\nretry_policy Constant {\n  max_retries 3\n  // Strategy is optional\n  strategy {\n    type constant_delay\n    delay_ms 200\n  }\n}\n\nretry_policy Exponential {\n  max_retries 2\n  // Strategy is optional\n  strategy {\n    type exponential_backoff\n    delay_ms 300\n    multiplier 1.5\n    max_delay_ms 10000\n  }\n}\n",
  "generators.baml": "// This helps use auto generate libraries you can use in the language of\n// your choice. You can have multiple generators if you use multiple languages.\n// Just ensure that the output_dir is different for each generator.\ngenerator target {\n    // Valid values: \"python/pydantic\", \"typescript\", \"ruby/sorbet\", \"rest/openapi\"\n    output_type \"typescript\"\n\n    // Where the generated code will be saved (relative to baml_src/)\n    output_dir \"../\"\n\n    // The version of the BAML package you have installed (e.g. same version as your baml-py or @boundaryml/baml).\n    // The BAML VSCode extension version should also match this version.\n    version \"0.202.1\"\n\n    // Valid values: \"sync\", \"async\"\n    // This controls what `b.FunctionName()` will be (sync or async).\n    default_client_mode async\n}\n",
  "tool_save_memory.baml": "// Save Memory Tool - 保存内存工具\n// 用于在会话之间保存和回忆信息\n\nclass SaveMemoryTool {\n    intent \"save_memory\" @description(\"保存一个事实或信息片段到内存中，以便在后续会话中回忆\")\n    fact string @description(\"要记住的具体事实或信息片段。这应该是一个用自然语言编写的清晰、自包含的陈述。\")\n}\n\nclass RecallMemoryTool {\n    intent \"recall_memory\" @description(\"回忆之前保存的内存信息\")\n    query string? @description(\"可选的查询字符串，用于搜索特定的内存信息。如果为空，则返回所有内存。\")\n}\n\nclass ClearMemoryTool {\n    intent \"clear_memory\" @description(\"清除所有或特定的内存信息\")\n    pattern string? @description(\"可选的模式字符串，用于匹配要清除的内存。如果为空，则清除所有内存。\")\n}\n\n// 内存工具类型联合\ntype MemoryTools = SaveMemoryTool | RecallMemoryTool | ClearMemoryTool\n",
}
export const getBamlFiles = () => {
    return fileMap;
}