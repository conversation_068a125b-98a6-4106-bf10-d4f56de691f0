"use strict";
/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
Object.defineProperty(exports, "__esModule", { value: true });
exports.HttpStreamRequest = exports.HttpRequest = void 0;
const baml_1 = require("@boundaryml/baml");
class HttpRequest {
    constructor(runtime, ctxManager) {
        this.runtime = runtime;
        this.ctxManager = ctxManager;
    }
    DetermineNextStep(thread, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.buildRequestSync("DetermineNextStep", {
                "thread": thread
            }, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, false, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ExtractWebData(content, data_type, selector, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.buildRequestSync("ExtractWebData", {
                "content": content, "data_type": data_type, "selector": selector ?? null
            }, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, false, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ProcessWebContent(content, instruction, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.buildRequestSync("ProcessWebContent", {
                "content": content, "instruction": instruction
            }, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, false, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    SummarizeWebContent(content, max_length, focus_area, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.buildRequestSync("SummarizeWebContent", {
                "content": content, "max_length": max_length ?? null, "focus_area": focus_area ?? null
            }, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, false, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
}
exports.HttpRequest = HttpRequest;
class HttpStreamRequest {
    constructor(runtime, ctxManager) {
        this.runtime = runtime;
        this.ctxManager = ctxManager;
    }
    DetermineNextStep(thread, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.buildRequestSync("DetermineNextStep", {
                "thread": thread
            }, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, true, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ExtractWebData(content, data_type, selector, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.buildRequestSync("ExtractWebData", {
                "content": content, "data_type": data_type, "selector": selector ?? null
            }, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, true, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ProcessWebContent(content, instruction, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.buildRequestSync("ProcessWebContent", {
                "content": content, "instruction": instruction
            }, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, true, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    SummarizeWebContent(content, max_length, focus_area, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.buildRequestSync("SummarizeWebContent", {
                "content": content, "max_length": max_length ?? null, "focus_area": focus_area ?? null
            }, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, true, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
}
exports.HttpStreamRequest = HttpStreamRequest;
//# sourceMappingURL=sync_request.js.map