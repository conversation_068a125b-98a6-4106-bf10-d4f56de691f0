"use strict";
/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
Object.defineProperty(exports, "__esModule", { value: true });
exports.getBamlFiles = void 0;
// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code
const fileMap = {
    "agent.baml": "class DoneForNow {\n  intent \"done_for_now\"\n  message string \n}\n\nfunction DetermineNextStep(\n    thread: string \n) -> WebFetchTools | DoneForNow {\n    client Qwen2_5_32B\n\n    prompt #\"\n        {{ _.role(\"system\") }}\n\n        You are a helpful web content assistant that can fetch, process, and analyze web content.\n        \n        Available tools:\n        - fetch_url: Fetch content from a URL\n        - process_content: Process fetched content with specific instructions\n        - extract_data: Extract specific data types from content (links, images, text, tables, metadata)\n        - summarize_content: Create summaries of web content\n        \n        Always be helpful and provide clear, actionable responses.\n\n        {{ _.role(\"user\") }}\n\n        You are working on the following thread:\n\n        {{ thread }}\n\n        What should the next step be? Choose the most appropriate tool or indicate if you're done.\n\n        {{ ctx.output_format }}\n    \"#\n}\n\nfunction ProcessWebContent(\n    content: string,\n    instruction: string\n) -> string {\n    client Qwen2_5_32B\n    \n    prompt #\"\n        {{ _.role(\"system\") }}\n        \n        You are an expert at processing and analyzing web content. \n        Process the following content according to the user's instructions.\n        \n        {{ _.role(\"user\") }}\n        \n        Content to process:\n        {{ content }}\n        \n        Instructions:\n        {{ instruction }}\n        \n        Please provide a clear and well-formatted response.\n    \"#\n}\n\nfunction ExtractWebData(\n    content: string,\n    data_type: string,\n    selector: string?\n) -> string {\n    client Qwen2_5_32B\n    \n    prompt #\"\n        {{ _.role(\"system\") }}\n        \n        You are an expert at extracting specific data from web content.\n        Extract the requested data type from the provided content.\n        \n        {{ _.role(\"user\") }}\n        \n        Content:\n        {{ content }}\n        \n        Data type to extract: {{ data_type }}\n        {% if selector %}\n        CSS Selector (if applicable): {{ selector }}\n        {% endif %}\n        \n        Please extract and format the requested data clearly.\n    \"#\n}\n\nfunction SummarizeWebContent(\n    content: string,\n    max_length: int?,\n    focus_area: string?\n) -> string {\n    client Qwen2_5_32B\n    \n    prompt #\"\n        {{ _.role(\"system\") }}\n        \n        You are an expert at creating concise and informative summaries of web content.\n        \n        {{ _.role(\"user\") }}\n        \n        Content to summarize:\n        {{ content }}\n        \n        {% if max_length %}\n        Maximum length: {{ max_length }} characters\n        {% endif %}\n        \n        {% if focus_area %}\n        Focus area: {{ focus_area }}\n        {% endif %}\n        \n        Please provide a clear, well-structured summary.\n    \"#\n}\n\ntest FetchWebsite {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n      {\n        \"type\": \"user_input\",\n        \"data\": \"Please fetch the content from https://example.com and summarize it\"\n      }\n    \"#\n  }\n}\n\ntest ProcessContent {\n  functions [DetermineNextStep]\n  args {\n    thread #\"\n      {\n        \"type\": \"user_input\",\n        \"data\": \"I have some HTML content that I need to extract all the links from\"\n      }\n    \"#\n  }\n}\n",
    "clients.baml": "// Learn more about clients at https://docs.boundaryml.com/docs/snippets/clients/overview\n\nclient<llm> Qwen2_5_32B {\n  provider openai\n  options {\n    model \"qwen2.5-32b-instruct-int4\"\n    api_key \"974fd8d1c155aa3d04b17bf253176b5e\"\n    base_url \"https://gateway.chat.sensedeal.vip/v1\"\n  }\n}\n\nclient<llm> CustomGPT4o {\n  provider openai\n  options {\n    model \"gpt-4o\"\n    api_key env.OPENAI_API_KEY\n  }\n}\n\nclient<llm> CustomGPT4oMini {\n  provider openai\n  retry_policy Exponential\n  options {\n    model \"gpt-4o-mini\"\n    api_key env.OPENAI_API_KEY\n  }\n}\n\nclient<llm> CustomSonnet {\n  provider anthropic\n  options {\n    model \"claude-3-5-sonnet-20241022\"\n    api_key env.ANTHROPIC_API_KEY\n  }\n}\n\nclient<llm> CustomHaiku {\n  provider anthropic\n  retry_policy Constant\n  options {\n    model \"claude-3-haiku-20240307\"\n    api_key env.ANTHROPIC_API_KEY\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/round-robin\nclient<llm> CustomFast {\n  provider round-robin\n  options {\n    // This will alternate between the two clients\n    strategy [CustomGPT4oMini, CustomHaiku]\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/fallback\nclient<llm> OpenaiFallback {\n  provider fallback\n  options {\n    // This will try the clients in order until one succeeds\n    strategy [CustomGPT4oMini, CustomGPT4oMini]\n  }\n}\n\n// https://docs.boundaryml.com/docs/snippets/clients/retry\nretry_policy Constant {\n  max_retries 3\n  // Strategy is optional\n  strategy {\n    type constant_delay\n    delay_ms 200\n  }\n}\n\nretry_policy Exponential {\n  max_retries 2\n  // Strategy is optional\n  strategy {\n    type exponential_backoff\n    delay_ms 300\n    multiplier 1.5\n    max_delay_ms 10000\n  }\n}\n",
    "tool_web_fetch.baml": "// Web Fetch Tool definitions\ntype WebFetchTools = FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool\n\nclass FetchUrlTool {\n    intent \"fetch_url\"\n    url string\n    description string?\n}\n\nclass ProcessContentTool {\n    intent \"process_content\"\n    content string\n    instruction string\n    format string? // \"text\" | \"json\" | \"markdown\"\n}\n\nclass ExtractDataTool {\n    intent \"extract_data\"\n    content string\n    data_type string // \"links\" | \"images\" | \"text\" | \"tables\" | \"metadata\"\n    selector string? // CSS selector for specific extraction\n}\n\nclass SummarizeContentTool {\n    intent \"summarize_content\"\n    content string\n    max_length int? // Maximum length of summary\n    focus_area string? // Specific area to focus on\n}\n\n// Response types\nclass WebFetchResult {\n    success bool\n    content string?\n    error_message string?\n    metadata WebMetadata?\n}\n\nclass WebMetadata {\n    title string?\n    description string?\n    url string\n    content_type string?\n    content_length int?\n    last_modified string?\n    status_code int?\n}\n",
};
const getBamlFiles = () => {
    return fileMap;
};
exports.getBamlFiles = getBamlFiles;
//# sourceMappingURL=inlinedbaml.js.map