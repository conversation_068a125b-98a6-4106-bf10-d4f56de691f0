{"version": 3, "file": "parser.js", "sourceRoot": "", "sources": ["../../baml_client/parser.ts"], "names": [], "mappings": ";AAAA;;;;;;;;kGAQkG;;;AAalG,2CAA8C;AAO9C,MAAa,iBAAiB;IAC5B,YAAoB,OAAoB,EAAU,UAA0B;QAAxD,YAAO,GAAP,OAAO,CAAa;QAAU,eAAU,GAAV,UAAU,CAAgB;IAAG,CAAC;IAGhF,iBAAiB,CACb,WAAmB,EACnB,gBAAkH;QAEpH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,mBAAmB,EACnB,WAAW,EACX,KAAK,EACL,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,EAC5B,gBAAgB,EAAE,cAAc,EAChC,GAAG,CACuF,CAAA;QAC9F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,cAAc,CACV,WAAmB,EACnB,gBAAkH;QAEpH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,gBAAgB,EAChB,WAAW,EACX,KAAK,EACL,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,EAC5B,gBAAgB,EAAE,cAAc,EAChC,GAAG,CACM,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,iBAAiB,CACb,WAAmB,EACnB,gBAAkH;QAEpH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,mBAAmB,EACnB,WAAW,EACX,KAAK,EACL,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,EAC5B,gBAAgB,EAAE,cAAc,EAChC,GAAG,CACM,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,mBAAmB,CACf,WAAmB,EACnB,gBAAkH;QAEpH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,qBAAqB,EACrB,WAAW,EACX,KAAK,EACL,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,EAC5B,gBAAgB,EAAE,cAAc,EAChC,GAAG,CACM,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;CAEF;AAhGD,8CAgGC;AAED,MAAa,eAAe;IAC1B,YAAoB,OAAoB,EAAU,UAA0B;QAAxD,YAAO,GAAP,OAAO,CAAa;QAAU,eAAU,GAAV,UAAU,CAAgB;IAAG,CAAC;IAGhF,iBAAiB,CACb,WAAmB,EACnB,gBAAkH;QAEpH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,mBAAmB,EACnB,WAAW,EACX,IAAI,EACJ,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,EAC5B,gBAAgB,EAAE,cAAc,EAChC,GAAG,CACuF,CAAA;QAC9F,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,cAAc,CACV,WAAmB,EACnB,gBAAkH;QAEpH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,gBAAgB,EAChB,WAAW,EACX,IAAI,EACJ,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,EAC5B,gBAAgB,EAAE,cAAc,EAChC,GAAG,CACM,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,iBAAiB,CACb,WAAmB,EACnB,gBAAkH;QAEpH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,mBAAmB,EACnB,WAAW,EACX,IAAI,EACJ,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,EAC5B,gBAAgB,EAAE,cAAc,EAChC,GAAG,CACM,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,mBAAmB,CACf,WAAmB,EACnB,gBAAkH;QAEpH,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,OAAO,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,qBAAqB,EACrB,WAAW,EACX,IAAI,EACJ,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,gBAAgB,EAAE,EAAE,EAAE,IAAI,EAAE,EAC5B,gBAAgB,EAAE,cAAc,EAChC,GAAG,CACM,CAAA;QACb,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;CAEF;AAhGD,0CAgGC"}