/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
import type { BamlRuntime, BamlCtxManager, ClientRegistry, Collector } from "@boundaryml/baml";
import { BamlStream } from "@boundaryml/baml";
import type { RecursivePartialNull as MovedRecursivePartialNull } from "./types";
import type { DoneForNow, ExtractDataTool, FetchUrlTool, ProcessContentTool, SummarizeContentTool } from "./types";
import type TypeBuilder from "./type_builder";
import { AsyncHttpRequest, AsyncHttpStreamRequest } from "./async_request";
import { LlmResponseParser, LlmStreamParser } from "./parser";
/**
 * @deprecated Use RecursivePartialNull from 'baml_client/types' instead.
 */
export type RecursivePartialNull<T> = MovedRecursivePartialNull<T>;
type BamlCallOptions = {
    tb?: TypeBuilder;
    clientRegistry?: ClientRegistry;
    collector?: Collector | Collector[];
    env?: Record<string, string | undefined>;
};
export declare class BamlAsyncClient {
    private runtime;
    private ctxManager;
    private streamClient;
    private httpRequest;
    private httpStreamRequest;
    private llmResponseParser;
    private llmStreamParser;
    private bamlOptions;
    constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager, bamlOptions?: BamlCallOptions);
    withOptions(bamlOptions: BamlCallOptions): BamlAsyncClient;
    get stream(): BamlStreamClient;
    get request(): AsyncHttpRequest;
    get streamRequest(): AsyncHttpStreamRequest;
    get parse(): LlmResponseParser;
    get parseStream(): LlmStreamParser;
    DetermineNextStep(thread: string, __baml_options__?: BamlCallOptions): Promise<FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool | DoneForNow>;
    ExtractWebData(content: string, data_type: string, selector?: string | null, __baml_options__?: BamlCallOptions): Promise<string>;
    ProcessWebContent(content: string, instruction: string, __baml_options__?: BamlCallOptions): Promise<string>;
    SummarizeWebContent(content: string, max_length?: number | null, focus_area?: string | null, __baml_options__?: BamlCallOptions): Promise<string>;
}
declare class BamlStreamClient {
    private runtime;
    private ctxManager;
    private bamlOptions;
    constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager, bamlOptions?: BamlCallOptions);
    DetermineNextStep(thread: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        collector?: Collector | Collector[];
        env?: Record<string, string | undefined>;
    }): BamlStream<FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool | DoneForNow, FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool | DoneForNow>;
    ExtractWebData(content: string, data_type: string, selector?: string | null, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        collector?: Collector | Collector[];
        env?: Record<string, string | undefined>;
    }): BamlStream<string, string>;
    ProcessWebContent(content: string, instruction: string, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        collector?: Collector | Collector[];
        env?: Record<string, string | undefined>;
    }): BamlStream<string, string>;
    SummarizeWebContent(content: string, max_length?: number | null, focus_area?: string | null, __baml_options__?: {
        tb?: TypeBuilder;
        clientRegistry?: ClientRegistry;
        collector?: Collector | Collector[];
        env?: Record<string, string | undefined>;
    }): BamlStream<string, string>;
}
export declare const b: BamlAsyncClient;
export {};
//# sourceMappingURL=async_client.d.ts.map