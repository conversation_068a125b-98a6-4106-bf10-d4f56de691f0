/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
import type { BamlRuntime, BamlCtxManager, ClientRegistry, Collector } from "@boundaryml/baml";
import type { RecursivePartialNull as MovedRecursivePartialNull } from "./types";
import type { <PERSON><PERSON><PERSON>Now, ExtractDataTool, FetchUrlTool, ProcessContentTool, SummarizeContentTool } from "./types";
import type TypeBuilder from "./type_builder";
import { HttpRequest, HttpStreamRequest } from "./sync_request";
import { LlmResponseParser, LlmStreamParser } from "./parser";
/**
 * @deprecated Use RecursivePartialNull from 'baml_client/types' instead.
 * Example:
 * ```ts
 * import { RecursivePartialNull } from './baml_client/types'
 * ```
 */
export type RecursivePartialNull<T> = MovedRecursivePartialNull<T>;
type BamlCallOptions = {
    tb?: TypeBuilder;
    clientRegistry?: ClientRegistry;
    collector?: Collector | Collector[];
    env?: Record<string, string | undefined>;
};
export declare class BamlSyncClient {
    private runtime;
    private ctxManager;
    private httpRequest;
    private httpStreamRequest;
    private llmResponseParser;
    private llmStreamParser;
    private bamlOptions;
    constructor(runtime: BamlRuntime, ctxManager: BamlCtxManager, bamlOptions?: BamlCallOptions);
    withOptions(bamlOptions: BamlCallOptions): BamlSyncClient;
    get stream(): void;
    get request(): HttpRequest;
    get streamRequest(): HttpStreamRequest;
    get parse(): LlmResponseParser;
    get parseStream(): LlmStreamParser;
    DetermineNextStep(thread: string, __baml_options__?: BamlCallOptions): FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool | DoneForNow;
    ExtractWebData(content: string, data_type: string, selector?: string | null, __baml_options__?: BamlCallOptions): string;
    ProcessWebContent(content: string, instruction: string, __baml_options__?: BamlCallOptions): string;
    SummarizeWebContent(content: string, max_length?: number | null, focus_area?: string | null, __baml_options__?: BamlCallOptions): string;
}
export declare const b: BamlSyncClient;
export {};
//# sourceMappingURL=sync_client.d.ts.map