"use strict";
/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
Object.defineProperty(exports, "__esModule", { value: true });
exports.LlmStreamParser = exports.LlmResponseParser = void 0;
const baml_1 = require("@boundaryml/baml");
class LlmResponseParser {
    constructor(runtime, ctxManager) {
        this.runtime = runtime;
        this.ctxManager = ctxManager;
    }
    DetermineNextStep(llmResponse, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.parseLlmResponse("DetermineNextStep", llmResponse, false, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ExtractWebData(llmResponse, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.parseLlmResponse("ExtractWebData", llmResponse, false, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ProcessWebContent(llmResponse, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.parseLlmResponse("ProcessWebContent", llmResponse, false, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    SummarizeWebContent(llmResponse, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.parseLlmResponse("SummarizeWebContent", llmResponse, false, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
}
exports.LlmResponseParser = LlmResponseParser;
class LlmStreamParser {
    constructor(runtime, ctxManager) {
        this.runtime = runtime;
        this.ctxManager = ctxManager;
    }
    DetermineNextStep(llmResponse, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.parseLlmResponse("DetermineNextStep", llmResponse, true, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ExtractWebData(llmResponse, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.parseLlmResponse("ExtractWebData", llmResponse, true, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ProcessWebContent(llmResponse, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.parseLlmResponse("ProcessWebContent", llmResponse, true, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    SummarizeWebContent(llmResponse, __baml_options__) {
        try {
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            return this.runtime.parseLlmResponse("SummarizeWebContent", llmResponse, true, this.ctxManager.cloneContext(), __baml_options__?.tb?.__tb(), __baml_options__?.clientRegistry, env);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
}
exports.LlmStreamParser = LlmStreamParser;
//# sourceMappingURL=parser.js.map