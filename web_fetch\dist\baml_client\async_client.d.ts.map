{"version": 3, "file": "async_client.d.ts", "sourceRoot": "", "sources": ["../../baml_client/async_client.ts"], "names": [], "mappings": "AAAA;;;;;;;;kGAQkG;AAYlG,OAAO,KAAK,EAAE,WAAW,EAAkB,cAAc,EAAE,cAAc,EAA4B,SAAS,EAAE,MAAM,kBAAkB,CAAA;AACxI,OAAO,EAAe,UAAU,EAAoB,MAAM,kBAAkB,CAAA;AAC5E,OAAO,KAAK,EAAkB,oBAAoB,IAAI,yBAAyB,EAAE,MAAM,SAAS,CAAA;AAGhG,OAAO,KAAK,EAAC,UAAU,EAAE,eAAe,EAAE,YAAY,EAAE,kBAAkB,EAAE,oBAAoB,EAA8B,MAAM,SAAS,CAAA;AAC7I,OAAO,KAAK,WAAW,MAAM,gBAAgB,CAAA;AAC7C,OAAO,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,MAAM,iBAAiB,CAAA;AAC1E,OAAO,EAAE,iBAAiB,EAAE,eAAe,EAAE,MAAM,UAAU,CAAA;AAG7D;;GAEG;AACH,MAAM,MAAM,oBAAoB,CAAC,CAAC,IAAI,yBAAyB,CAAC,CAAC,CAAC,CAAA;AAElE,KAAK,eAAe,GAAG;IACrB,EAAE,CAAC,EAAE,WAAW,CAAA;IAChB,cAAc,CAAC,EAAE,cAAc,CAAA;IAC/B,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,EAAE,CAAA;IACnC,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAA;CACzC,CAAA;AAED,qBAAa,eAAe;IAC1B,OAAO,CAAC,OAAO,CAAa;IAC5B,OAAO,CAAC,UAAU,CAAgB;IAClC,OAAO,CAAC,YAAY,CAAkB;IACtC,OAAO,CAAC,WAAW,CAAkB;IACrC,OAAO,CAAC,iBAAiB,CAAwB;IACjD,OAAO,CAAC,iBAAiB,CAAmB;IAC5C,OAAO,CAAC,eAAe,CAAiB;IACxC,OAAO,CAAC,WAAW,CAAiB;gBAExB,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,CAAC,EAAE,eAAe;IAW3F,WAAW,CAAC,WAAW,EAAE,eAAe;IAIxC,IAAI,MAAM,qBAET;IAED,IAAI,OAAO,qBAEV;IAED,IAAI,aAAa,2BAEhB;IAED,IAAI,KAAK,sBAER;IAED,IAAI,WAAW,oBAEd;IAGK,iBAAiB,CACnB,MAAM,EAAE,MAAM,EACd,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,YAAY,GAAG,kBAAkB,GAAG,eAAe,GAAG,oBAAoB,GAAG,UAAU,CAAC;IAyB7F,cAAc,CAChB,OAAO,EAAE,MAAM,EAAC,SAAS,EAAE,MAAM,EAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,EAC1D,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,MAAM,CAAC;IAyBZ,iBAAiB,CACnB,OAAO,EAAE,MAAM,EAAC,WAAW,EAAE,MAAM,EACnC,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,MAAM,CAAC;IAyBZ,mBAAmB,CACrB,OAAO,EAAE,MAAM,EAAC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,EAAC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,EACrE,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,MAAM,CAAC;CAyBnB;AAED,cAAM,gBAAgB;IACpB,OAAO,CAAC,OAAO,CAAa;IAC5B,OAAO,CAAC,UAAU,CAAgB;IAClC,OAAO,CAAC,WAAW,CAAiB;gBAExB,OAAO,EAAE,WAAW,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,CAAC,EAAE,eAAe;IAO3F,iBAAiB,CACb,MAAM,EAAE,MAAM,EACd,gBAAgB,CAAC,EAAE;QAAE,EAAE,CAAC,EAAE,WAAW,CAAC;QAAC,cAAc,CAAC,EAAE,cAAc,CAAC;QAAC,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,EAAE,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAA;KAAE,GACxJ,UAAU,CAAC,YAAY,GAAG,kBAAkB,GAAG,eAAe,GAAG,oBAAoB,GAAG,UAAU,EAAE,YAAY,GAAG,kBAAkB,GAAG,eAAe,GAAG,oBAAoB,GAAG,UAAU,CAAC;IA+B/L,cAAc,CACV,OAAO,EAAE,MAAM,EAAC,SAAS,EAAE,MAAM,EAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,EAC1D,gBAAgB,CAAC,EAAE;QAAE,EAAE,CAAC,EAAE,WAAW,CAAC;QAAC,cAAc,CAAC,EAAE,cAAc,CAAC;QAAC,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,EAAE,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAA;KAAE,GACxJ,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC;IA+B7B,iBAAiB,CACb,OAAO,EAAE,MAAM,EAAC,WAAW,EAAE,MAAM,EACnC,gBAAgB,CAAC,EAAE;QAAE,EAAE,CAAC,EAAE,WAAW,CAAC;QAAC,cAAc,CAAC,EAAE,cAAc,CAAC;QAAC,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,EAAE,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAA;KAAE,GACxJ,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC;IA+B7B,mBAAmB,CACf,OAAO,EAAE,MAAM,EAAC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,EAAC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,EACrE,gBAAgB,CAAC,EAAE;QAAE,EAAE,CAAC,EAAE,WAAW,CAAC;QAAC,cAAc,CAAC,EAAE,cAAc,CAAC;QAAC,SAAS,CAAC,EAAE,SAAS,GAAG,SAAS,EAAE,CAAC;QAAC,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAA;KAAE,GACxJ,UAAU,CAAC,MAAM,EAAE,MAAM,CAAC;CA+B9B;AAED,eAAO,MAAM,CAAC,iBAA8I,CAAA"}