/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

import { FieldType } from '@boundaryml/baml/native'
import { TypeBuilder as _TypeBuilder, EnumBuilder, EnumViewer, ClassBuilder, ClassViewer } from '@boundaryml/baml/type_builder'
import { DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME } from "./globals"

export default class TypeBuilder {
    private tb: _TypeBuilder;
    
    DoneForNow: ClassViewer<'DoneForNow', "intent" | "message">;
    
    ExtractDataTool: ClassViewer<'ExtractDataTool', "intent" | "content" | "data_type" | "selector">;
    
    FetchUrlTool: ClassViewer<'FetchUrlTool', "intent" | "url" | "description">;
    
    ProcessContentTool: ClassViewer<'ProcessContentTool', "intent" | "content" | "instruction" | "format">;
    
    SummarizeContentTool: ClassViewer<'SummarizeContentTool', "intent" | "content" | "max_length" | "focus_area">;
    
    WebFetchResult: ClassViewer<'WebFetchResult', "success" | "content" | "error_message" | "metadata">;
    
    WebMetadata: ClassViewer<'WebMetadata', "title" | "description" | "url" | "content_type" | "content_length" | "last_modified" | "status_code">;
    
    

    constructor() {
        this.tb = new _TypeBuilder({
          classes: new Set([
            "DoneForNow","ExtractDataTool","FetchUrlTool","ProcessContentTool","SummarizeContentTool","WebFetchResult","WebMetadata",
          ]),
          enums: new Set([
            
          ]),
          runtime: DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME
        });
        
        this.DoneForNow = this.tb.classViewer("DoneForNow", [
          "intent","message",
        ]);
        
        this.ExtractDataTool = this.tb.classViewer("ExtractDataTool", [
          "intent","content","data_type","selector",
        ]);
        
        this.FetchUrlTool = this.tb.classViewer("FetchUrlTool", [
          "intent","url","description",
        ]);
        
        this.ProcessContentTool = this.tb.classViewer("ProcessContentTool", [
          "intent","content","instruction","format",
        ]);
        
        this.SummarizeContentTool = this.tb.classViewer("SummarizeContentTool", [
          "intent","content","max_length","focus_area",
        ]);
        
        this.WebFetchResult = this.tb.classViewer("WebFetchResult", [
          "success","content","error_message","metadata",
        ]);
        
        this.WebMetadata = this.tb.classViewer("WebMetadata", [
          "title","description","url","content_type","content_length","last_modified","status_code",
        ]);
        
        
    }

    __tb() {
      return this.tb._tb();
    }

    string(): FieldType {
        return this.tb.string()
    }

    literalString(value: string): FieldType {
        return this.tb.literalString(value)
    }

    literalInt(value: number): FieldType {
        return this.tb.literalInt(value)
    }

    literalBool(value: boolean): FieldType {
        return this.tb.literalBool(value)
    }

    int(): FieldType {
        return this.tb.int()
    }

    float(): FieldType {
        return this.tb.float()
    }

    bool(): FieldType {
        return this.tb.bool()
    }

    list(type: FieldType): FieldType {
        return this.tb.list(type)
    }

    null(): FieldType {
        return this.tb.null()
    }

    map(key: FieldType, value: FieldType): FieldType {
        return this.tb.map(key, value)
    }

    union(types: FieldType[]): FieldType {
        return this.tb.union(types)
    }

    addClass<Name extends string>(name: Name): ClassBuilder<Name> {
        return this.tb.addClass(name);
    }

    addEnum<Name extends string>(name: Name): EnumBuilder<Name> {
        return this.tb.addEnum(name);
    }

    addBaml(baml: string): void {
        this.tb.addBaml(baml);
    }
}
