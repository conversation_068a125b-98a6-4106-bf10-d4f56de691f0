#!/usr/bin/env node

import * as readline from "readline";
import { WebFetchAgent, Thread } from "./agent";

class WebFetchCLI {
  private agent: WebFetchAgent;
  private rl: readline.Interface;

  constructor() {
    this.agent = new WebFetchAgent();
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
    });
  }

  private displayWelcome() {
    console.log("\n🌐 Web Fetch Tool - 12-Factor Agents Framework");
    console.log("=".repeat(50));
    console.log("Available commands:");
    console.log("  fetch <url>                    - Fetch content from URL");
    console.log(
      "  process <instruction>          - Process last fetched content"
    );
    console.log(
      "  extract <type> [selector]      - Extract data (links, images, text, tables, metadata)"
    );
    console.log("  summarize [max_length] [focus] - Summarize content");
    console.log("  help                           - Show this help");
    console.log("  exit                           - Exit the tool");
    console.log("=".repeat(50));
    console.log("You can also provide natural language instructions!\n");
  }

  private parseCommand(input: string): { command: string; args: string[] } {
    const parts = input.trim().split(/\s+/);
    const command = parts[0].toLowerCase();
    const args = parts.slice(1);
    return { command, args };
  }

  private async handleDirectCommand(
    command: string,
    args: string[],
    thread: Thread,
    input: string = ""
  ): Promise<Thread> {
    switch (command) {
      case "fetch":
        if (args.length === 0) {
          console.log("❌ Please provide a URL to fetch");
          return thread;
        }
        const url = args[0];
        const description = args.slice(1).join(" ") || undefined;

        thread.addEvent("user_input", {
          command: "fetch_url",
          url,
          description,
        });
        break;

      case "process":
        if (args.length === 0) {
          console.log("❌ Please provide processing instructions");
          return thread;
        }
        const instruction = args.join(" ");
        const content = this.agent.getLatestContent(thread);

        if (!content) {
          console.log("❌ No content available. Please fetch a URL first.");
          return thread;
        }

        thread.addEvent("user_input", {
          command: "process_content",
          content,
          instruction,
        });
        break;

      case "extract":
        if (args.length === 0) {
          console.log(
            "❌ Please specify data type to extract (links, images, text, tables, metadata)"
          );
          return thread;
        }
        const dataType = args[0];
        const selector = args[1] || undefined;
        const extractContent = this.agent.getLatestContent(thread);

        if (!extractContent) {
          console.log("❌ No content available. Please fetch a URL first.");
          return thread;
        }

        thread.addEvent("user_input", {
          command: "extract_data",
          content: extractContent,
          data_type: dataType,
          selector,
        });
        break;

      case "summarize":
        const maxLength = args[0] ? parseInt(args[0]) : undefined;
        const focusArea = args.slice(1).join(" ") || undefined;
        const summarizeContent = this.agent.getLatestContent(thread);

        if (!summarizeContent) {
          console.log("❌ No content available. Please fetch a URL first.");
          return thread;
        }

        thread.addEvent("user_input", {
          command: "summarize_content",
          content: summarizeContent,
          max_length: maxLength,
          focus_area: focusArea,
        });
        break;

      case "help":
        this.displayWelcome();
        return thread;

      default:
        // Treat as natural language input
        thread.addEvent("user_input", {
          data: input,
        });
        break;
    }

    return thread;
  }

  private displayResults(thread: Thread) {
    const lastEvent = thread.getLastEvent();

    if (!lastEvent) return;

    switch (lastEvent.type) {
      case "url_fetched":
        console.log("\n✅ URL Fetched Successfully");
        console.log(`📄 Title: ${lastEvent.data.metadata?.title || "N/A"}`);
        console.log(
          `📏 Content Length: ${lastEvent.data.content.length} characters`
        );
        console.log(`🔗 URL: ${lastEvent.data.url}`);
        if (lastEvent.data.metadata?.description) {
          console.log(`📝 Description: ${lastEvent.data.metadata.description}`);
        }
        break;

      case "content_processed":
        console.log("\n✅ Content Processed");
        console.log(`📋 Instruction: ${lastEvent.data.instruction}`);
        console.log(`📄 Result:\n${lastEvent.data.processed_content}`);
        break;

      case "data_extracted":
        console.log("\n✅ Data Extracted");
        console.log(`🔍 Type: ${lastEvent.data.data_type}`);
        console.log(`📊 Count: ${lastEvent.data.count} items`);
        if (lastEvent.data.direct_results.length > 0) {
          console.log("📋 Results:");
          lastEvent.data.direct_results
            .slice(0, 10)
            .forEach((item: string, index: number) => {
              console.log(`  ${index + 1}. ${item}`);
            });
          if (lastEvent.data.direct_results.length > 10) {
            console.log(
              `  ... and ${
                lastEvent.data.direct_results.length - 10
              } more items`
            );
          }
        }
        break;

      case "content_summarized":
        console.log("\n✅ Content Summarized");
        console.log(
          `📏 Original: ${lastEvent.data.original_length} characters`
        );
        console.log(`📄 Summary:\n${lastEvent.data.summary}`);
        break;

      case "fetch_error":
      case "processing_error":
      case "extraction_error":
      case "summarization_error":
      case "tool_error":
      case "agent_error":
        console.log(`\n❌ Error: ${lastEvent.data.error}`);
        break;
    }
  }

  async run() {
    this.displayWelcome();

    const thread = new Thread();

    while (true) {
      try {
        const input = await new Promise<string>((resolve) => {
          this.rl.question("🌐 web-fetch> ", resolve);
        });

        if (input.trim().toLowerCase() === "exit") {
          console.log("👋 Goodbye!");
          break;
        }

        if (input.trim() === "") {
          continue;
        }

        const { command, args } = this.parseCommand(input);

        // Handle direct commands or natural language
        const updatedThread = await this.handleDirectCommand(
          command,
          args,
          thread,
          input
        );

        // Run agent loop if there's new input
        if (updatedThread.events.length > thread.events.length) {
          console.log("\n🤖 Processing...");
          const result = await this.agent.agentLoop(updatedThread);

          // Display results
          this.displayResults(updatedThread);

          // Display final message if provided
          if (result && result.trim()) {
            console.log(`\n💬 Agent: ${result}`);
          }
        }
      } catch (error: any) {
        console.error("\n❌ Error:", error.message);
      }
    }

    this.rl.close();
  }
}

// Run CLI if this file is executed directly
if (require.main === module) {
  const cli = new WebFetchCLI();
  cli.run().catch(console.error);
}

export { WebFetchCLI };
