{"version": 3, "file": "async_client.js", "sourceRoot": "", "sources": ["../../baml_client/async_client.ts"], "names": [], "mappings": ";AAAA;;;;;;;;kGAQkG;;;AAalG,2CAA4E;AAM5E,mDAA0E;AAC1E,qCAA6D;AAC7D,uCAAkJ;AAclJ,MAAa,eAAe;IAU1B,YAAY,OAAoB,EAAE,UAA0B,EAAE,WAA6B;QACzF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,YAAY,GAAG,IAAI,gBAAgB,CAAC,OAAO,EAAE,UAAU,EAAE,WAAW,CAAC,CAAA;QAC1E,IAAI,CAAC,WAAW,GAAG,IAAI,gCAAgB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;QAC5D,IAAI,CAAC,iBAAiB,GAAG,IAAI,sCAAsB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;QACxE,IAAI,CAAC,iBAAiB,GAAG,IAAI,0BAAiB,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;QACnE,IAAI,CAAC,eAAe,GAAG,IAAI,wBAAe,CAAC,OAAO,EAAE,UAAU,CAAC,CAAA;QAC/D,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,EAAE,CAAA;IACtC,CAAC;IAED,WAAW,CAAC,WAA4B;QACtC,OAAO,IAAI,eAAe,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,WAAW,CAAC,CAAA;IACxE,CAAC;IAED,IAAI,MAAM;QACR,OAAO,IAAI,CAAC,YAAY,CAAA;IAC1B,CAAC;IAED,IAAI,OAAO;QACT,OAAO,IAAI,CAAC,WAAW,CAAA;IACzB,CAAC;IAED,IAAI,aAAa;QACf,OAAO,IAAI,CAAC,iBAAiB,CAAA;IAC/B,CAAC;IAED,IAAI,KAAK;QACP,OAAO,IAAI,CAAC,iBAAiB,CAAA;IAC/B,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,eAAe,CAAA;IAC7B,CAAC;IAGD,KAAK,CAAC,iBAAiB,CACnB,MAAc,EACd,gBAAkC;QAEpC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAAA;YACpE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CACzC,mBAAmB,EACnB;gBACE,QAAQ,EAAE,MAAM;aACjB,EACD,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAClB,OAAO,CAAC,cAAc,EACtB,SAAS,EACT,GAAG,CACJ,CAAA;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAA4F,CAAA;QACrH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAChB,OAAe,EAAC,SAAiB,EAAC,QAAwB,EAC1D,gBAAkC;QAEpC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAAA;YACpE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CACzC,gBAAgB,EAChB;gBACE,SAAS,EAAE,OAAO,EAAC,WAAW,EAAE,SAAS,EAAC,UAAU,EAAE,QAAQ,IAAG,IAAI;aACtE,EACD,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAClB,OAAO,CAAC,cAAc,EACtB,SAAS,EACT,GAAG,CACJ,CAAA;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAW,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CACnB,OAAe,EAAC,WAAmB,EACnC,gBAAkC;QAEpC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAAA;YACpE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CACzC,mBAAmB,EACnB;gBACE,SAAS,EAAE,OAAO,EAAC,aAAa,EAAE,WAAW;aAC9C,EACD,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAClB,OAAO,CAAC,cAAc,EACtB,SAAS,EACT,GAAG,CACJ,CAAA;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAW,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CACrB,OAAe,EAAC,UAA0B,EAAC,UAA0B,EACrE,gBAAkC;QAEpC,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAAA;YACpE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,MAAM,GAAG,GAAG,MAAM,IAAI,CAAC,OAAO,CAAC,YAAY,CACzC,qBAAqB,EACrB;gBACE,SAAS,EAAE,OAAO,EAAC,YAAY,EAAE,UAAU,IAAG,IAAI,EAAC,YAAY,EAAE,UAAU,IAAG,IAAI;aACnF,EACD,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAClB,OAAO,CAAC,cAAc,EACtB,SAAS,EACT,GAAG,CACJ,CAAA;YACD,OAAO,GAAG,CAAC,MAAM,CAAC,KAAK,CAAW,CAAA;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;CAEF;AA9JD,0CA8JC;AAED,MAAM,gBAAgB;IAKpB,YAAY,OAAoB,EAAE,UAA0B,EAAE,WAA6B;QACzF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAA;QACtB,IAAI,CAAC,UAAU,GAAG,UAAU,CAAA;QAC5B,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,EAAE,CAAA;IACtC,CAAC;IAGD,iBAAiB,CACb,MAAc,EACd,gBAAuJ;QAEzJ,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAAA;YACpE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CACrC,mBAAmB,EACnB;gBACE,QAAQ,EAAE,MAAM;aACjB,EACD,SAAS,EACT,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAClB,OAAO,CAAC,cAAc,EACtB,SAAS,EACT,GAAG,CACJ,CAAA;YACD,OAAO,IAAI,iBAAU,CACnB,GAAG,EACH,CAAC,CAAC,EAA2F,EAAE,CAAC,CAAC,EACjG,CAAC,CAAC,EAA2F,EAAE,CAAC,CAAC,EACjG,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAC/B,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,cAAc,CACV,OAAe,EAAC,SAAiB,EAAC,QAAwB,EAC1D,gBAAuJ;QAEzJ,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAAA;YACpE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CACrC,gBAAgB,EAChB;gBACE,SAAS,EAAE,OAAO,EAAC,WAAW,EAAE,SAAS,EAAC,UAAU,EAAE,QAAQ,IAAI,IAAI;aACvE,EACD,SAAS,EACT,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAClB,OAAO,CAAC,cAAc,EACtB,SAAS,EACT,GAAG,CACJ,CAAA;YACD,OAAO,IAAI,iBAAU,CACnB,GAAG,EACH,CAAC,CAAC,EAAU,EAAE,CAAC,CAAC,EAChB,CAAC,CAAC,EAAU,EAAE,CAAC,CAAC,EAChB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAC/B,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,iBAAiB,CACb,OAAe,EAAC,WAAmB,EACnC,gBAAuJ;QAEzJ,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAAA;YACpE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CACrC,mBAAmB,EACnB;gBACE,SAAS,EAAE,OAAO,EAAC,aAAa,EAAE,WAAW;aAC9C,EACD,SAAS,EACT,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAClB,OAAO,CAAC,cAAc,EACtB,SAAS,EACT,GAAG,CACJ,CAAA;YACD,OAAO,IAAI,iBAAU,CACnB,GAAG,EACH,CAAC,CAAC,EAAU,EAAE,CAAC,CAAC,EAChB,CAAC,CAAC,EAAU,EAAE,CAAC,CAAC,EAChB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAC/B,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;IAED,mBAAmB,CACf,OAAe,EAAC,UAA0B,EAAC,UAA0B,EACrE,gBAAuJ;QAEzJ,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,gBAAgB,IAAI,EAAE,CAAC,EAAE,CAAA;YACpE,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;YACxH,MAAM,MAAM,GAAG,gBAAgB,EAAE,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,GAAG,gBAAgB,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,OAAO,CAAC,GAAG,EAAE,CAAC;YACxG,MAAM,GAAG,GAA2B,MAAM,CAAC,WAAW,CACpD,MAAM,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,EAAE,CAAC,KAAK,KAAK,SAAS,CAAuB,CACzF,CAAC;YACF,MAAM,GAAG,GAAG,IAAI,CAAC,OAAO,CAAC,cAAc,CACrC,qBAAqB,EACrB;gBACE,SAAS,EAAE,OAAO,EAAC,YAAY,EAAE,UAAU,IAAI,IAAI,EAAC,YAAY,EAAE,UAAU,IAAI,IAAI;aACrF,EACD,SAAS,EACT,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,EAC9B,OAAO,CAAC,EAAE,EAAE,IAAI,EAAE,EAClB,OAAO,CAAC,cAAc,EACtB,SAAS,EACT,GAAG,CACJ,CAAA;YACD,OAAO,IAAI,iBAAU,CACnB,GAAG,EACH,CAAC,CAAC,EAAU,EAAE,CAAC,CAAC,EAChB,CAAC,CAAC,EAAU,EAAE,CAAC,CAAC,EAChB,IAAI,CAAC,UAAU,CAAC,YAAY,EAAE,CAC/B,CAAA;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,MAAM,IAAA,kBAAW,EAAC,KAAK,CAAC,CAAC;QAC3B,CAAC;IACH,CAAC;CAEF;AAEY,QAAA,CAAC,GAAG,IAAI,eAAe,CAAC,sEAA4D,EAAE,kEAAwD,CAAC,CAAA"}