#!/usr/bin/env ts-node

import { WebFetcher } from './src/webFetcher';

async function testWebFetcher() {
    console.log('🧪 Testing WebFetcher directly...\n');
    
    const fetcher = new WebFetcher({
        timeout: 10000,
        maxContentLength: 50000
    });

    // Test 1: Basic URL fetch
    console.log('📝 Test 1: Basic URL fetch');
    try {
        const result = await fetcher.fetchUrl('https://httpbin.org/json');
        if (result.success) {
            console.log('✅ Success!');
            console.log(`📄 Content length: ${result.content?.length}`);
            console.log(`🔗 URL: ${result.metadata?.url}`);
            console.log(`📊 Status: ${result.metadata?.statusCode}`);
        } else {
            console.log('❌ Failed:', result.errorMessage);
        }
    } catch (error: any) {
        console.log('❌ Error:', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 2: HTML content fetch and parsing
    console.log('📝 Test 2: HTML content fetch and parsing');
    try {
        const result = await fetcher.fetchUrl('https://example.com');
        if (result.success && result.content) {
            console.log('✅ Success!');
            console.log(`📄 Title: ${result.metadata?.title}`);
            console.log(`📏 Content length: ${result.content.length}`);
            
            // Test HTML to text conversion
            const textContent = fetcher.convertHtmlToText(result.content);
            console.log(`📝 Text content preview: ${textContent.substring(0, 100)}...`);
            
            // Test link extraction
            const links = fetcher.extractData(result.content, 'links');
            console.log(`🔗 Links found: ${links.length}`);
            links.forEach((link, index) => {
                console.log(`  ${index + 1}. ${link}`);
            });
            
        } else {
            console.log('❌ Failed:', result.errorMessage);
        }
    } catch (error: any) {
        console.log('❌ Error:', error.message);
    }

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 3: Private IP detection
    console.log('📝 Test 3: Private IP detection');
    const testUrls = [
        'https://example.com',
        'http://localhost:3000',
        'http://***********',
        'http://********',
        'http://127.0.0.1'
    ];

    testUrls.forEach(url => {
        const isPrivate = fetcher.isPrivateIp(url);
        console.log(`${isPrivate ? '🔒' : '🌐'} ${url} - ${isPrivate ? 'Private' : 'Public'}`);
    });

    console.log('\n' + '='.repeat(50) + '\n');

    // Test 4: Error handling
    console.log('📝 Test 4: Error handling');
    try {
        const result = await fetcher.fetchUrl('https://this-domain-does-not-exist-12345.com');
        if (!result.success) {
            console.log('✅ Error handling works!');
            console.log(`❌ Error: ${result.errorMessage}`);
        } else {
            console.log('❓ Unexpected success');
        }
    } catch (error: any) {
        console.log('✅ Error handling works!');
        console.log(`❌ Error: ${error.message}`);
    }

    console.log('\n🎉 WebFetcher tests completed!');
}

// Run tests if this file is executed directly
if (require.main === module) {
    testWebFetcher().catch(console.error);
}
