"use strict";
/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
Object.defineProperty(exports, "__esModule", { value: true });
exports.b = exports.BamlAsyncClient = void 0;
const baml_1 = require("@boundaryml/baml");
const async_request_1 = require("./async_request");
const parser_1 = require("./parser");
const globals_1 = require("./globals");
class BamlAsyncClient {
    constructor(runtime, ctxManager, bamlOptions) {
        this.runtime = runtime;
        this.ctxManager = ctxManager;
        this.streamClient = new BamlStreamClient(runtime, ctxManager, bamlOptions);
        this.httpRequest = new async_request_1.AsyncHttpRequest(runtime, ctxManager);
        this.httpStreamRequest = new async_request_1.AsyncHttpStreamRequest(runtime, ctxManager);
        this.llmResponseParser = new parser_1.LlmResponseParser(runtime, ctxManager);
        this.llmStreamParser = new parser_1.LlmStreamParser(runtime, ctxManager);
        this.bamlOptions = bamlOptions || {};
    }
    withOptions(bamlOptions) {
        return new BamlAsyncClient(this.runtime, this.ctxManager, bamlOptions);
    }
    get stream() {
        return this.streamClient;
    }
    get request() {
        return this.httpRequest;
    }
    get streamRequest() {
        return this.httpStreamRequest;
    }
    get parse() {
        return this.llmResponseParser;
    }
    get parseStream() {
        return this.llmStreamParser;
    }
    async DetermineNextStep(thread, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = await this.runtime.callFunction("DetermineNextStep", {
                "thread": thread
            }, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return raw.parsed(false);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    async ExtractWebData(content, data_type, selector, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = await this.runtime.callFunction("ExtractWebData", {
                "content": content, "data_type": data_type, "selector": selector ?? null
            }, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return raw.parsed(false);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    async ProcessWebContent(content, instruction, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = await this.runtime.callFunction("ProcessWebContent", {
                "content": content, "instruction": instruction
            }, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return raw.parsed(false);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    async SummarizeWebContent(content, max_length, focus_area, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = await this.runtime.callFunction("SummarizeWebContent", {
                "content": content, "max_length": max_length ?? null, "focus_area": focus_area ?? null
            }, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return raw.parsed(false);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
}
exports.BamlAsyncClient = BamlAsyncClient;
class BamlStreamClient {
    constructor(runtime, ctxManager, bamlOptions) {
        this.runtime = runtime;
        this.ctxManager = ctxManager;
        this.bamlOptions = bamlOptions || {};
    }
    DetermineNextStep(thread, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = this.runtime.streamFunction("DetermineNextStep", {
                "thread": thread
            }, undefined, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return new baml_1.BamlStream(raw, (a) => a, (a) => a, this.ctxManager.cloneContext());
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ExtractWebData(content, data_type, selector, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = this.runtime.streamFunction("ExtractWebData", {
                "content": content, "data_type": data_type, "selector": selector ?? null
            }, undefined, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return new baml_1.BamlStream(raw, (a) => a, (a) => a, this.ctxManager.cloneContext());
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ProcessWebContent(content, instruction, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = this.runtime.streamFunction("ProcessWebContent", {
                "content": content, "instruction": instruction
            }, undefined, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return new baml_1.BamlStream(raw, (a) => a, (a) => a, this.ctxManager.cloneContext());
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    SummarizeWebContent(content, max_length, focus_area, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = this.runtime.streamFunction("SummarizeWebContent", {
                "content": content, "max_length": max_length ?? null, "focus_area": focus_area ?? null
            }, undefined, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return new baml_1.BamlStream(raw, (a) => a, (a) => a, this.ctxManager.cloneContext());
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
}
exports.b = new BamlAsyncClient(globals_1.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME, globals_1.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX);
//# sourceMappingURL=async_client.js.map