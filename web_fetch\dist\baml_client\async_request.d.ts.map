{"version": 3, "file": "async_request.d.ts", "sourceRoot": "", "sources": ["../../baml_client/async_request.ts"], "names": [], "mappings": "AAAA;;;;;;;;kGAQkG;AAYlG,OAAO,KAAK,EAAE,WAAW,EAAE,cAAc,EAAE,cAAc,EAA4B,MAAM,kBAAkB,CAAA;AAC7G,OAAO,EAAe,WAAW,EAAE,MAAM,kBAAkB,CAAA;AAI3D,OAAO,KAAK,WAAW,MAAM,gBAAgB,CAAA;AAE7C,KAAK,eAAe,GAAG;IACrB,EAAE,CAAC,EAAE,WAAW,CAAA;IAChB,cAAc,CAAC,EAAE,cAAc,CAAA;IAC/B,GAAG,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAA;CACzC,CAAA;AAED,qBAAa,gBAAgB;IACf,OAAO,CAAC,OAAO;IAAe,OAAO,CAAC,UAAU;gBAAxC,OAAO,EAAE,WAAW,EAAU,UAAU,EAAE,cAAc;IAGtE,iBAAiB,CACnB,MAAM,EAAE,MAAM,EACd,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,WAAW,CAAC;IAsBjB,cAAc,CAChB,OAAO,EAAE,MAAM,EAAC,SAAS,EAAE,MAAM,EAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,EAC1D,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,WAAW,CAAC;IAsBjB,iBAAiB,CACnB,OAAO,EAAE,MAAM,EAAC,WAAW,EAAE,MAAM,EACnC,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,WAAW,CAAC;IAsBjB,mBAAmB,CACrB,OAAO,EAAE,MAAM,EAAC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,EAAC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,EACrE,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,WAAW,CAAC;CAsBxB;AAED,qBAAa,sBAAsB;IACrB,OAAO,CAAC,OAAO;IAAe,OAAO,CAAC,UAAU;gBAAxC,OAAO,EAAE,WAAW,EAAU,UAAU,EAAE,cAAc;IAGtE,iBAAiB,CACnB,MAAM,EAAE,MAAM,EACd,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,WAAW,CAAC;IAsBjB,cAAc,CAChB,OAAO,EAAE,MAAM,EAAC,SAAS,EAAE,MAAM,EAAC,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,EAC1D,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,WAAW,CAAC;IAsBjB,iBAAiB,CACnB,OAAO,EAAE,MAAM,EAAC,WAAW,EAAE,MAAM,EACnC,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,WAAW,CAAC;IAsBjB,mBAAmB,CACrB,OAAO,EAAE,MAAM,EAAC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,EAAC,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,EACrE,gBAAgB,CAAC,EAAE,eAAe,GACnC,OAAO,CAAC,WAAW,CAAC;CAsBxB"}