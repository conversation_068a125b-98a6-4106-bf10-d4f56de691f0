"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebFetchAgent = exports.Thread = void 0;
const baml_client_1 = require("../baml_client");
const webFetcher_1 = require("./webFetcher");
class Thread {
    constructor(events = []) {
        this.events = [];
        this.events = events;
    }
    addEvent(type, data) {
        this.events.push({
            type,
            data,
            timestamp: new Date().toISOString(),
        });
    }
    serializeForLLM() {
        // Serialize events for LLM consumption
        return JSON.stringify(this.events, null, 2);
    }
    getLastEvent() {
        return this.events[this.events.length - 1];
    }
    getEventsByType(type) {
        return this.events.filter((event) => event.type === type);
    }
}
exports.Thread = Thread;
class WebFetchAgent {
    constructor() {
        this.webFetcher = new webFetcher_1.WebFetcher({
            timeout: 15000,
            maxContentLength: 200000,
            userAgent: "WebFetch-Agent/1.0.0 (12-factor-agents)",
        });
    }
    async handleNextStep(nextStep, thread) {
        console.log("Executing tool:", nextStep.intent);
        try {
            switch (nextStep.intent) {
                case "fetch_url":
                    return await this.handleFetchUrl(nextStep, thread);
                case "process_content":
                    return await this.handleProcessContent(nextStep, thread);
                case "extract_data":
                    return await this.handleExtractData(nextStep, thread);
                case "summarize_content":
                    return await this.handleSummarizeContent(nextStep, thread);
                default:
                    throw new Error(`Unknown tool intent: ${nextStep.intent}`);
            }
        }
        catch (error) {
            console.error("Error executing tool:", error.message);
            thread.addEvent("tool_error", {
                intent: nextStep.intent,
                error: error.message,
            });
            return thread;
        }
    }
    async handleFetchUrl(tool, thread) {
        console.log(`Fetching URL: ${tool.url}`);
        const result = await this.webFetcher.fetchUrl(tool.url, tool.description || undefined);
        if (result.success && result.content) {
            thread.addEvent("url_fetched", {
                url: tool.url,
                content: result.content,
                metadata: result.metadata,
                description: tool.description,
            });
            console.log(`Successfully fetched ${result.content.length} characters from ${tool.url}`);
        }
        else {
            thread.addEvent("fetch_error", {
                url: tool.url,
                error: result.errorMessage,
                description: tool.description,
            });
            console.error(`Failed to fetch ${tool.url}: ${result.errorMessage}`);
        }
        return thread;
    }
    async handleProcessContent(tool, thread) {
        console.log("Processing content with instruction:", tool.instruction);
        try {
            const processedContent = await baml_client_1.b.ProcessWebContent(tool.content, tool.instruction);
            thread.addEvent("content_processed", {
                instruction: tool.instruction,
                original_length: tool.content.length,
                processed_content: processedContent,
                format: tool.format || "text",
            });
            console.log("Content processed successfully");
        }
        catch (error) {
            thread.addEvent("processing_error", {
                instruction: tool.instruction,
                error: error.message,
            });
            console.error("Error processing content:", error.message);
        }
        return thread;
    }
    async handleExtractData(tool, thread) {
        console.log(`Extracting ${tool.data_type} data`);
        try {
            // Use both AI extraction and direct parsing
            const directExtraction = this.webFetcher.extractData(tool.content, tool.data_type, tool.selector || undefined);
            const aiExtraction = await baml_client_1.b.ExtractWebData(tool.content, tool.data_type, tool.selector || undefined);
            thread.addEvent("data_extracted", {
                data_type: tool.data_type,
                selector: tool.selector,
                direct_results: directExtraction,
                ai_results: aiExtraction,
                count: directExtraction.length,
            });
            console.log(`Extracted ${directExtraction.length} items of type ${tool.data_type}`);
        }
        catch (error) {
            thread.addEvent("extraction_error", {
                data_type: tool.data_type,
                error: error.message,
            });
            console.error("Error extracting data:", error.message);
        }
        return thread;
    }
    async handleSummarizeContent(tool, thread) {
        console.log("Summarizing content");
        try {
            const summary = await baml_client_1.b.SummarizeWebContent(tool.content, tool.max_length, tool.focus_area);
            thread.addEvent("content_summarized", {
                original_length: tool.content.length,
                summary: summary,
                max_length: tool.max_length,
                focus_area: tool.focus_area,
            });
            console.log(`Content summarized: ${tool.content.length} -> ${summary.length} characters`);
        }
        catch (error) {
            thread.addEvent("summarization_error", {
                error: error.message,
            });
            console.error("Error summarizing content:", error.message);
        }
        return thread;
    }
    async agentLoop(thread) {
        let iterations = 0;
        const maxIterations = 10; // Prevent infinite loops
        while (iterations < maxIterations) {
            iterations++;
            try {
                const nextStep = await baml_client_1.b.DetermineNextStep(thread.serializeForLLM());
                console.log(`Iteration ${iterations} - Next step:`, nextStep.intent);
                thread.addEvent("tool_call", {
                    iteration: iterations,
                    tool: nextStep,
                });
                switch (nextStep.intent) {
                    case "done_for_now":
                        console.log("Agent completed task");
                        return nextStep.message;
                    case "fetch_url":
                    case "process_content":
                    case "extract_data":
                    case "summarize_content":
                        thread = await this.handleNextStep(nextStep, thread);
                        break;
                    default:
                        console.warn("Unknown intent:", nextStep.intent);
                        return `Unknown action requested: ${nextStep.intent}`;
                }
            }
            catch (error) {
                console.error("Error in agent loop:", error.message);
                thread.addEvent("agent_error", {
                    iteration: iterations,
                    error: error.message,
                });
                return `Error occurred: ${error.message}`;
            }
        }
        return `Maximum iterations (${maxIterations}) reached. Task may be incomplete.`;
    }
    // Helper method to get content from thread
    getLatestContent(thread) {
        const fetchEvents = thread.getEventsByType("url_fetched");
        if (fetchEvents.length > 0) {
            return fetchEvents[fetchEvents.length - 1].data.content;
        }
        return null;
    }
    // Helper method to get processing results
    getLatestProcessingResult(thread) {
        const processEvents = thread.getEventsByType("content_processed");
        if (processEvents.length > 0) {
            return processEvents[processEvents.length - 1].data.processed_content;
        }
        return null;
    }
}
exports.WebFetchAgent = WebFetchAgent;
//# sourceMappingURL=agent.js.map