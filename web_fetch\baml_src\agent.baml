// Web fetch agent implementation
// Handles user requests for web content fetching and processing

function DetermineWebFetchAction(
    user_request: string,
    context: string?
) -> AllWebFetchTools {
    client Dou<PERSON><PERSON>

    prompt #"
        {{ _.role("system") }}
        你是一个专门处理网页内容获取的助手。你可以帮助用户获取网页内容、转换格式和生成摘要。

        你有以下工具可用：
        1. web_fetch - 获取单个网页内容并转换为指定格式
        2. web_fetch_multiple - 批量获取多个网页内容
        3. web_fetch_summary - 获取网页内容并生成摘要
        4. request_more_information - 请求用户提供更多信息
        5. done_for_now - 完成当前任务

        支持的功能：
        - 获取网页内容并转换为 Markdown、纯文本或保留 HTML 格式
        - 批量处理多个网页
        - 生成网页内容摘要
        - 使用 CSS 选择器提取特定内容
        - 控制输出长度和格式

        {{ _.role("user") }}
        {% if context %}
        上下文信息：
        {{ context }}
        {% endif %}

        用户请求：{{ user_request }}

        基于用户的请求，确定下一步应该做什么。如果用户提供了 URL，请使用相应的工具获取内容。
        如果请求不明确，请要求澄清。

        Answer in JSON using any of these schemas:
        {
          // 获取网页内容并转换为 Markdown 格式
          intent: "web_fetch",
          // 要获取的网页 URL。必须是有效的 HTTP 或 HTTPS URL。
          url: string,
          // 输出格式，可选值：'markdown'（默认）、'text'、'html'。
          format: string or null,
          // 返回内容的最大长度（字符数），默认为 10000。
          max_length: int or null,
          // 是否在 Markdown 中包含链接，默认为 true。
          include_links: bool or null,
          // 可选的 CSS 选择器，用于提取页面的特定部分。
          selector: string or null,
        } or {
          // 批量获取多个网页内容
          intent: "web_fetch_multiple",
          // 要获取的网页 URL 列表。每个 URL 必须是有效的 HTTP 或 HTTPS URL。
          urls: string[],
          // 输出格式，可选值：'markdown'（默认）、'text'、'html'。
          format: string or null,
          // 每个页面返回内容的最大长度（字符数），默认为 5000。
          max_length: int or null,
          // 是否在 Markdown 中包含链接，默认为 true。
          include_links: bool or null,
        } or {
          // 获取网页内容并生成摘要
          intent: "web_fetch_summary",
          // 要获取和摘要的网页 URL。
          url: string,
          // 摘要长度：'short'（简短）、'medium'（中等）、'long'（详细），默认为 'medium'。
          summary_length: string or null,
          // 摘要的重点主题或关键词，用于生成针对性摘要。
          focus_topic: string or null,
        } or {
          intent: "done_for_now",
          // 发送给用户的关于已完成工作的消息
          message: string,
        } or {
          // 请求用户提供更多信息
          intent: "request_more_information",
          // 向用户请求澄清的消息
          message: string,
        }
    "#
}

// Test cases for web fetch functionality
test WebFetchBasic {
    functions [DetermineWebFetchAction]
    args {
        user_request "请获取 https://vibecoding.sop.best/chat 的内容"
        context null
    }
    @@assert(intent, {{this.intent == "web_fetch"}})
    @@assert(url, {{this.url == "https://vibecoding.sop.best/chat"}})
}

test WebFetchWithFormat {
    functions [DetermineWebFetchAction]
    args {
        user_request "获取 https://news.ycombinator.com 的内容，转换为纯文本格式"
        context null
    }
    @@assert(intent, {{this.intent == "web_fetch"}})
    @@assert(url, {{this.url == "https://news.ycombinator.com"}})
    @@assert(format, {{this.format == "text"}})
}

test WebFetchMultiple {
    functions [DetermineWebFetchAction]
    args {
        user_request "批量获取这些网页的内容：https://example.com, https://google.com"
        context null
    }
    @@assert(intent, {{this.intent == "web_fetch_multiple"}})
    @@assert(urls_count, {{this.urls | length > 1}})
}

test WebFetchSummary {
    functions [DetermineWebFetchAction]
    args {
        user_request "获取 https://en.wikipedia.org/wiki/Artificial_intelligence 的内容并生成摘要"
        context null
    }
    @@assert(intent, {{this.intent == "web_fetch_summary"}})
    @@assert(url_contains, {{"wikipedia" in this.url}})
}

test WebFetchWithSelector {
    functions [DetermineWebFetchAction]
    args {
        user_request "获取 https://github.com 主页的导航菜单内容，使用选择器 .header-nav"
        context null
    }
    @@assert(intent, {{this.intent == "web_fetch"}})
    @@assert(has_selector, {{this.selector}})
}

test RequestClarification {
    functions [DetermineWebFetchAction]
    args {
        user_request "获取那个网站的内容"
        context null
    }
    @@assert(intent, {{this.intent == "request_more_information"}})
}

test CompleteTask {
    functions [DetermineWebFetchAction]
    args {
        user_request "谢谢，内容已经获取完成了"
        context "已成功获取 https://example.com 的内容"
    }
    @@assert(intent, {{this.intent == "done_for_now"}})
}
