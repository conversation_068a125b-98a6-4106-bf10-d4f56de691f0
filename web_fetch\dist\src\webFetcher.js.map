{"version": 3, "file": "webFetcher.js", "sourceRoot": "", "sources": ["../../src/webFetcher.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,4DAA+B;AAC/B,+CAAuC;AACvC,iDAAmC;AACnC,6BAA0B;AA0B1B,MAAa,UAAU;IAQnB,YAAoB,UAA2B,EAAE;QAA7B,YAAO,GAAP,OAAO,CAAsB;QAPhC,mBAAc,GAA8B;YACzD,OAAO,EAAE,KAAK;YACd,gBAAgB,EAAE,MAAM;YACxB,SAAS,EAAE,qBAAqB;YAChC,eAAe,EAAE,IAAI;SACxB,CAAC;QAGE,IAAI,CAAC,OAAO,GAAG,EAAE,GAAG,IAAI,CAAC,cAAc,EAAE,GAAG,OAAO,EAAE,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,GAAW,EAAE,WAAoB;QAC5C,IAAI,CAAC;YACD,eAAe;YACf,MAAM,SAAS,GAAG,IAAI,SAAG,CAAC,GAAG,CAAC,CAAC;YAC/B,IAAI,CAAC,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACpD,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAC;YAC9D,CAAC;YAED,OAAO,CAAC,GAAG,CAAC,iBAAiB,GAAG,GAAG,WAAW,CAAC,CAAC,CAAC,KAAK,WAAW,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;YAE7E,sCAAsC;YACtC,MAAM,UAAU,GAAG,IAAI,eAAe,EAAE,CAAC;YACzC,MAAM,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE,IAAI,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC;YAE7E,IAAI,CAAC;gBACD,MAAM,QAAQ,GAAG,MAAM,IAAA,oBAAK,EAAC,GAAG,EAAE;oBAC9B,MAAM,EAAE,UAAU,CAAC,MAAM;oBACzB,OAAO,EAAE;wBACL,YAAY,EAAE,IAAI,CAAC,OAAO,CAAC,SAAU;wBACrC,QAAQ,EAAE,iEAAiE;wBAC3E,iBAAiB,EAAE,gBAAgB;wBACnC,iBAAiB,EAAE,eAAe;wBAClC,YAAY,EAAE,YAAY;wBAC1B,2BAA2B,EAAE,GAAG;qBACnC;oBACD,QAAQ,EAAE,IAAI,CAAC,OAAO,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ;iBAC/D,CAAC,CAAC;gBAEH,YAAY,CAAC,SAAS,CAAC,CAAC;gBAExB,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;oBACf,MAAM,IAAI,KAAK,CAAC,QAAQ,QAAQ,CAAC,MAAM,KAAK,QAAQ,CAAC,UAAU,EAAE,CAAC,CAAC;gBACvE,CAAC;gBAED,MAAM,WAAW,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;gBAC/D,MAAM,aAAa,GAAG,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,GAAG,CAAC,CAAC;gBAE9E,uBAAuB;gBACvB,IAAI,aAAa,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAiB,EAAE,CAAC;oBACjD,MAAM,IAAI,KAAK,CAAC,sBAAsB,aAAa,gBAAgB,IAAI,CAAC,OAAO,CAAC,gBAAgB,GAAG,CAAC,CAAC;gBACzG,CAAC;gBAED,MAAM,UAAU,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAEzC,uBAAuB;gBACvB,MAAM,OAAO,GAAG,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,gBAAiB;oBAC9D,CAAC,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,gBAAiB,CAAC,GAAG,gBAAgB;oBAC5E,CAAC,CAAC,UAAU,CAAC;gBAEjB,mBAAmB;gBACnB,MAAM,QAAQ,GAAgB;oBAC1B,GAAG;oBACH,WAAW;oBACX,aAAa,EAAE,OAAO,CAAC,MAAM;oBAC7B,YAAY,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,SAAS;oBAChE,UAAU,EAAE,QAAQ,CAAC,MAAM;iBAC9B,CAAC;gBAEF,0CAA0C;gBAC1C,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;oBACpC,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;oBAChC,QAAQ,CAAC,KAAK,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,SAAS,CAAC;oBAC/D,QAAQ,CAAC,WAAW,GAAG,CAAC,CAAC,0BAA0B,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC;wBAC/C,CAAC,CAAC,iCAAiC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,SAAS,CAAC;gBAC3F,CAAC;gBAED,OAAO;oBACH,OAAO,EAAE,IAAI;oBACb,OAAO;oBACP,QAAQ;iBACX,CAAC;YAEN,CAAC;oBAAS,CAAC;gBACP,YAAY,CAAC,SAAS,CAAC,CAAC;YAC5B,CAAC;QAEL,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YAClB,OAAO,CAAC,KAAK,CAAC,kBAAkB,GAAG,GAAG,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;YAEvD,OAAO;gBACH,OAAO,EAAE,KAAK;gBACd,YAAY,EAAE,KAAK,CAAC,OAAO,IAAI,wBAAwB;gBACvD,QAAQ,EAAE;oBACN,GAAG;oBACH,UAAU,EAAE,CAAC;iBAChB;aACJ,CAAC;QACN,CAAC;IACL,CAAC;IAED,iBAAiB,CAAC,IAAY;QAC1B,OAAO,IAAA,sBAAO,EAAC,IAAI,EAAE;YACjB,QAAQ,EAAE,KAAK;YACf,SAAS,EAAE;gBACP,EAAE,QAAQ,EAAE,GAAG,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE,IAAI,EAAE,EAAE;gBAChD,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;gBACnC,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE;gBACtC,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE;gBACrC,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE;gBACnC,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,MAAM,EAAE;aACzC;SACJ,CAAC,CAAC;IACP,CAAC;IAED,WAAW,CAAC,OAAe,EAAE,QAAgB,EAAE,QAAiB;QAC5D,MAAM,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;QAChC,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,QAAQ,QAAQ,CAAC,WAAW,EAAE,EAAE,CAAC;YAC7B,KAAK,OAAO;gBACR,CAAC,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;oBAC7B,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;oBACrC,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;oBACtC,IAAI,IAAI,EAAE,CAAC;wBACP,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,IAAI,IAAI,KAAK,IAAI,EAAE,CAAC,CAAC;oBAC7C,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV,KAAK,QAAQ;gBACT,CAAC,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;oBAC9B,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;oBACnC,MAAM,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC;oBACzC,IAAI,GAAG,EAAE,CAAC;wBACN,OAAO,CAAC,IAAI,CAAC,GAAG,GAAG,KAAK,GAAG,EAAE,CAAC,CAAC;oBACnC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV,KAAK,MAAM;gBACP,IAAI,QAAQ,EAAE,CAAC;oBACX,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;wBAC5B,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;wBACtC,IAAI,IAAI;4BAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjC,CAAC,CAAC,CAAC;gBACP,CAAC;qBAAM,CAAC;oBACJ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC;gBAClD,CAAC;gBACD,MAAM;YAEV,KAAK,QAAQ;gBACT,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,EAAE,EAAE;oBACzB,MAAM,SAAS,GAAa,EAAE,CAAC;oBAC/B,CAAC,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,GAAG,EAAE,EAAE;wBAChC,MAAM,OAAO,GAAa,EAAE,CAAC;wBAC7B,CAAC,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,EAAE;4BACnC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC;wBACxC,CAAC,CAAC,CAAC;wBACH,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;4BACrB,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;wBACxC,CAAC;oBACL,CAAC,CAAC,CAAC;oBACH,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;wBACvB,OAAO,CAAC,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACvC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV,KAAK,UAAU;gBACX,CAAC,CAAC,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;oBAC1B,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;oBACpE,MAAM,OAAO,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;oBAC3C,IAAI,IAAI,IAAI,OAAO,EAAE,CAAC;wBAClB,OAAO,CAAC,IAAI,CAAC,GAAG,IAAI,KAAK,OAAO,EAAE,CAAC,CAAC;oBACxC,CAAC;gBACL,CAAC,CAAC,CAAC;gBACH,MAAM;YAEV;gBACI,IAAI,QAAQ,EAAE,CAAC;oBACX,CAAC,CAAC,QAAQ,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE,EAAE;wBAC5B,MAAM,IAAI,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,EAAE,CAAC,IAAI,EAAE,CAAC;wBACtC,IAAI,IAAI;4BAAE,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;oBACjC,CAAC,CAAC,CAAC;gBACP,CAAC;gBACD,MAAM;QACd,CAAC;QAED,OAAO,OAAO,CAAC;IACnB,CAAC;IAED,WAAW,CAAC,GAAW;QACnB,IAAI,CAAC;YACD,MAAM,QAAQ,GAAG,IAAI,SAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,CAAC;YACvC,MAAM,aAAa,GAAG;gBAClB,OAAO;gBACP,QAAQ;gBACR,gCAAgC;gBAChC,aAAa;gBACb,OAAO;gBACP,QAAQ;gBACR,QAAQ;gBACR,cAAc;aACjB,CAAC;YAEF,OAAO,aAAa,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC;QAC7D,CAAC;QAAC,MAAM,CAAC;YACL,OAAO,KAAK,CAAC;QACjB,CAAC;IACL,CAAC;CACJ;AApND,gCAoNC"}