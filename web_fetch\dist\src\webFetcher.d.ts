export interface WebFetchOptions {
    timeout?: number;
    maxContentLength?: number;
    userAgent?: string;
    followRedirects?: boolean;
}
export interface WebMetadata {
    title?: string;
    description?: string;
    url: string;
    contentType?: string;
    contentLength?: number;
    lastModified?: string;
    statusCode?: number;
}
export interface WebFetchResult {
    success: boolean;
    content?: string;
    errorMessage?: string;
    metadata?: WebMetadata;
}
export declare class WebFetcher {
    private options;
    private readonly defaultOptions;
    constructor(options?: WebFetchOptions);
    fetchUrl(url: string, description?: string): Promise<WebFetchResult>;
    convertHtmlToText(html: string): string;
    extractData(content: string, dataType: string, selector?: string): string[];
    isPrivateIp(url: string): boolean;
}
//# sourceMappingURL=webFetcher.d.ts.map