/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
/******************************************************************************
*
*  These types are used for streaming, for when an instance of a type
*  is still being built up and any of its fields is not yet fully available.
*
******************************************************************************/
export interface StreamState<T> {
    value: T;
    state: "Pending" | "Incomplete" | "Complete";
}
export declare namespace partial_types {
    interface DoneForNow {
        intent?: "done_for_now" | null;
        message?: string | null;
    }
    interface ExtractDataTool {
        intent?: "extract_data" | null;
        content?: string | null;
        data_type?: string | null;
        selector?: string | null;
    }
    interface FetchUrlTool {
        intent?: "fetch_url" | null;
        url?: string | null;
        description?: string | null;
    }
    interface ProcessContentTool {
        intent?: "process_content" | null;
        content?: string | null;
        instruction?: string | null;
        format?: string | null;
    }
    interface SummarizeContentTool {
        intent?: "summarize_content" | null;
        content?: string | null;
        max_length?: number | null;
        focus_area?: string | null;
    }
    interface WebFetchResult {
        success?: boolean | null;
        content?: string | null;
        error_message?: string | null;
        metadata?: WebMetadata | null;
    }
    interface WebMetadata {
        title?: string | null;
        description?: string | null;
        url?: string | null;
        content_type?: string | null;
        content_length?: number | null;
        last_modified?: string | null;
        status_code?: number | null;
    }
    type WebFetchTools = FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool | null;
}
//# sourceMappingURL=partial_types.d.ts.map