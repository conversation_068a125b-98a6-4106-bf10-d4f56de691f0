{"version": 3, "file": "type_builder.js", "sourceRoot": "", "sources": ["../../baml_client/type_builder.ts"], "names": [], "mappings": ";AAAA;;;;;;;;kGAQkG;;AAalG,gEAA+H;AAC/H,uCAAwF;AAExF,MAAqB,WAAW;IAmB5B;QACI,IAAI,CAAC,EAAE,GAAG,IAAI,0BAAY,CAAC;YACzB,OAAO,EAAE,IAAI,GAAG,CAAC;gBACf,YAAY,EAAC,iBAAiB,EAAC,cAAc,EAAC,oBAAoB,EAAC,sBAAsB,EAAC,gBAAgB,EAAC,aAAa;aACzH,CAAC;YACF,KAAK,EAAE,IAAI,GAAG,CAAC,EAEd,CAAC;YACF,OAAO,EAAE,sEAA4D;SACtE,CAAC,CAAC;QAEH,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,YAAY,EAAE;YAClD,QAAQ,EAAC,SAAS;SACnB,CAAC,CAAC;QAEH,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,iBAAiB,EAAE;YAC5D,QAAQ,EAAC,SAAS,EAAC,WAAW,EAAC,UAAU;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,cAAc,EAAE;YACtD,QAAQ,EAAC,KAAK,EAAC,aAAa;SAC7B,CAAC,CAAC;QAEH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,oBAAoB,EAAE;YAClE,QAAQ,EAAC,SAAS,EAAC,aAAa,EAAC,QAAQ;SAC1C,CAAC,CAAC;QAEH,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,sBAAsB,EAAE;YACtE,QAAQ,EAAC,SAAS,EAAC,YAAY,EAAC,YAAY;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,gBAAgB,EAAE;YAC1D,SAAS,EAAC,SAAS,EAAC,eAAe,EAAC,UAAU;SAC/C,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,aAAa,EAAE;YACpD,OAAO,EAAC,aAAa,EAAC,KAAK,EAAC,cAAc,EAAC,gBAAgB,EAAC,eAAe,EAAC,aAAa;SAC1F,CAAC,CAAC;IAGP,CAAC;IAED,IAAI;QACF,OAAO,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC;IACvB,CAAC;IAED,MAAM;QACF,OAAO,IAAI,CAAC,EAAE,CAAC,MAAM,EAAE,CAAA;IAC3B,CAAC;IAED,aAAa,CAAC,KAAa;QACvB,OAAO,IAAI,CAAC,EAAE,CAAC,aAAa,CAAC,KAAK,CAAC,CAAA;IACvC,CAAC;IAED,UAAU,CAAC,KAAa;QACpB,OAAO,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,KAAK,CAAC,CAAA;IACpC,CAAC;IAED,WAAW,CAAC,KAAc;QACtB,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,CAAC,KAAK,CAAC,CAAA;IACrC,CAAC;IAED,GAAG;QACC,OAAO,IAAI,CAAC,EAAE,CAAC,GAAG,EAAE,CAAA;IACxB,CAAC;IAED,KAAK;QACD,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE,CAAA;IAC1B,CAAC;IAED,IAAI;QACA,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;IACzB,CAAC;IAED,IAAI,CAAC,IAAe;QAChB,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,CAAA;IAC7B,CAAC;IAED,IAAI;QACA,OAAO,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAA;IACzB,CAAC;IAED,GAAG,CAAC,GAAc,EAAE,KAAgB;QAChC,OAAO,IAAI,CAAC,EAAE,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAA;IAClC,CAAC;IAED,KAAK,CAAC,KAAkB;QACpB,OAAO,IAAI,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,CAAA;IAC/B,CAAC;IAED,QAAQ,CAAsB,IAAU;QACpC,OAAO,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;IAClC,CAAC;IAED,OAAO,CAAsB,IAAU;QACnC,OAAO,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IACjC,CAAC;IAED,OAAO,CAAC,IAAY;QAChB,IAAI,CAAC,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAC1B,CAAC;CACJ;AAxHD,8BAwHC"}