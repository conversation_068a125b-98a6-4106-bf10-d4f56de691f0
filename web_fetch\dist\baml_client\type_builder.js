"use strict";
/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
Object.defineProperty(exports, "__esModule", { value: true });
const type_builder_1 = require("@boundaryml/baml/type_builder");
const globals_1 = require("./globals");
class TypeBuilder {
    constructor() {
        this.tb = new type_builder_1.TypeBuilder({
            classes: new Set([
                "DoneForNow", "ExtractDataTool", "FetchUrlTool", "ProcessContentTool", "SummarizeContentTool", "WebFetchResult", "WebMetadata",
            ]),
            enums: new Set([]),
            runtime: globals_1.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME
        });
        this.DoneForNow = this.tb.classViewer("DoneForNow", [
            "intent", "message",
        ]);
        this.ExtractDataTool = this.tb.classViewer("ExtractDataTool", [
            "intent", "content", "data_type", "selector",
        ]);
        this.FetchUrlTool = this.tb.classViewer("FetchUrlTool", [
            "intent", "url", "description",
        ]);
        this.ProcessContentTool = this.tb.classViewer("ProcessContentTool", [
            "intent", "content", "instruction", "format",
        ]);
        this.SummarizeContentTool = this.tb.classViewer("SummarizeContentTool", [
            "intent", "content", "max_length", "focus_area",
        ]);
        this.WebFetchResult = this.tb.classViewer("WebFetchResult", [
            "success", "content", "error_message", "metadata",
        ]);
        this.WebMetadata = this.tb.classViewer("WebMetadata", [
            "title", "description", "url", "content_type", "content_length", "last_modified", "status_code",
        ]);
    }
    __tb() {
        return this.tb._tb();
    }
    string() {
        return this.tb.string();
    }
    literalString(value) {
        return this.tb.literalString(value);
    }
    literalInt(value) {
        return this.tb.literalInt(value);
    }
    literalBool(value) {
        return this.tb.literalBool(value);
    }
    int() {
        return this.tb.int();
    }
    float() {
        return this.tb.float();
    }
    bool() {
        return this.tb.bool();
    }
    list(type) {
        return this.tb.list(type);
    }
    null() {
        return this.tb.null();
    }
    map(key, value) {
        return this.tb.map(key, value);
    }
    union(types) {
        return this.tb.union(types);
    }
    addClass(name) {
        return this.tb.addClass(name);
    }
    addEnum(name) {
        return this.tb.addEnum(name);
    }
    addBaml(baml) {
        this.tb.addBaml(baml);
    }
}
exports.default = TypeBuilder;
//# sourceMappingURL=type_builder.js.map