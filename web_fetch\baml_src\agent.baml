class DoneForNow {
  intent "done_for_now"
  message string
}

function DetermineNextStep(
    thread: string
) -> WebFetchTools | DoneForNow {
    client DouBao

    prompt #"
        {{ _.role("system") }}

        你是一个有用的网页内容助手，可以获取、处理和分析网页内容。

        可用工具：
        - fetch_url: 从URL获取内容
        - process_content: 根据特定指令处理获取的内容
        - extract_data: 从内容中提取特定数据类型（链接、图片、文本、表格、元数据）
        - summarize_content: 创建网页内容摘要

        始终保持有用并提供清晰、可操作的回应。

        {{ _.role("user") }}

        你正在处理以下线程：

        {{ thread }}

        下一步应该做什么？选择最合适的工具或表示你已完成。

        {{ ctx.output_format }}
    "#
}

function ProcessWebContent(
    content: string,
    instruction: string
) -> string {
    client DouBao

    prompt #"
        {{ _.role("system") }}

        你是处理和分析网页内容的专家。
        请根据用户的指令处理以下内容。

        {{ _.role("user") }}

        要处理的内容：
        {{ content }}

        指令：
        {{ instruction }}

        请提供清晰且格式良好的回应。
    "#
}

function ExtractWebData(
    content: string,
    data_type: string,
    selector: string?
) -> string {
    client DouBao

    prompt #"
        {{ _.role("system") }}

        你是从网页内容中提取特定数据的专家。
        从提供的内容中提取请求的数据类型。

        {{ _.role("user") }}

        内容：
        {{ content }}

        要提取的数据类型：{{ data_type }}
        {% if selector %}
        CSS选择器（如适用）：{{ selector }}
        {% endif %}

        请清晰地提取并格式化请求的数据。
    "#
}

function SummarizeWebContent(
    content: string,
    max_length: int?,
    focus_area: string?
) -> string {
    client DouBao

    prompt #"
        {{ _.role("system") }}

        你是创建简洁且信息丰富的网页内容摘要的专家。

        {{ _.role("user") }}

        要总结的内容：
        {{ content }}

        {% if max_length %}
        最大长度：{{ max_length }} 个字符
        {% endif %}

        {% if focus_area %}
        重点关注区域：{{ focus_area }}
        {% endif %}

        请提供清晰、结构良好的摘要。
    "#
}

test FetchWebsite {
  functions [DetermineNextStep]
  args {
    thread #"
      {
        "type": "user_input",
        "data": "请从 https://example.com 获取内容并进行总结"
      }
    "#
  }
}

test ProcessContent {
  functions [DetermineNextStep]
  args {
    thread #"
      {
        "type": "user_input",
        "data": "我有一些HTML内容，需要从中提取所有链接"
      }
    "#
  }
}
