/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
/**
 * Recursively partial type that can be null.
 *
 * @deprecated Use types from the `partial_types` namespace instead, which provides type-safe partial implementations
 * @template T The type to make recursively partial.
 */
export type RecursivePartialNull<T> = T extends object ? {
    [P in keyof T]?: RecursivePartialNull<T[P]>;
} : T | null;
export interface Checked<T, CheckName extends string = string> {
    value: T;
    checks: Record<CheckName, Check>;
}
export interface Check {
    name: string;
    expr: string;
    status: "succeeded" | "failed";
}
export declare function all_succeeded<CheckName extends string>(checks: Record<CheckName, Check>): boolean;
export declare function get_checks<CheckName extends string>(checks: Record<CheckName, Check>): Check[];
export interface DoneForNow {
    intent: "done_for_now";
    message: string;
}
export interface ExtractDataTool {
    intent: "extract_data";
    content: string;
    data_type: string;
    selector?: string | null;
}
export interface FetchUrlTool {
    intent: "fetch_url";
    url: string;
    description?: string | null;
}
export interface ProcessContentTool {
    intent: "process_content";
    content: string;
    instruction: string;
    format?: string | null;
}
export interface SummarizeContentTool {
    intent: "summarize_content";
    content: string;
    max_length?: number | null;
    focus_area?: string | null;
}
export interface WebFetchResult {
    success: boolean;
    content?: string | null;
    error_message?: string | null;
    metadata?: WebMetadata | null;
}
export interface WebMetadata {
    title?: string | null;
    description?: string | null;
    url: string;
    content_type?: string | null;
    content_length?: number | null;
    last_modified?: string | null;
    status_code?: number | null;
}
export type WebFetchTools = FetchUrlTool | ProcessContentTool | ExtractDataTool | SummarizeContentTool;
//# sourceMappingURL=types.d.ts.map