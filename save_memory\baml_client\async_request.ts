/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/

// This file was generated by BAML: please do not edit it. Instead, edit the
// BAML files and re-generate this code using: baml-cli generate
// You can install baml-cli with:
//  $ npm install @boundaryml/baml
//
/* eslint-disable */
// tslint:disable
// @ts-nocheck
// biome-ignore format: autogenerated code

import type { BamlRuntime, BamlCtxManager, ClientRegistry, Image, Audio, Pdf, Video } from "@boundaryml/baml"
import { toBamlError, HTTPRequest } from "@boundaryml/baml"
import type { Checked, Check } from "./types"
import type * as types from "./types"
import type {ClarificationRequest, ClearMemoryTool, DoneForNow, RecallMemoryTool, SaveMemoryTool} from "./types"
import type TypeBuilder from "./type_builder"

type BamlCallOptions = {
  tb?: TypeBuilder
  clientRegistry?: ClientRegistry
  env?: Record<string, string | undefined>
}

export class AsyncHttpRequest {
  constructor(private runtime: BamlRuntime, private ctxManager: BamlCtxManager) {}

  
  async DetermineNextStep(
      thread: string,memory_context?: string | null,
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      const env: Record<string, string> = Object.fromEntries(
        Object.entries(rawEnv).filter(([_, value]) => value !== undefined) as [string, string][]
      );
      return await this.runtime.buildRequest(
        "DetermineNextStep",
        {
          "thread": thread,"memory_context": memory_context?? null
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        false,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}

export class AsyncHttpStreamRequest {
  constructor(private runtime: BamlRuntime, private ctxManager: BamlCtxManager) {}

  
  async DetermineNextStep(
      thread: string,memory_context?: string | null,
      __baml_options__?: BamlCallOptions
  ): Promise<HTTPRequest> {
    try {
      const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
      const env: Record<string, string> = Object.fromEntries(
        Object.entries(rawEnv).filter(([_, value]) => value !== undefined) as [string, string][]
      );
      return await this.runtime.buildRequest(
        "DetermineNextStep",
        {
          "thread": thread,"memory_context": memory_context?? null
        },
        this.ctxManager.cloneContext(),
        __baml_options__?.tb?.__tb(),
        __baml_options__?.clientRegistry,
        true,
        env
      )
    } catch (error) {
      throw toBamlError(error);
    }
  }
  
}
