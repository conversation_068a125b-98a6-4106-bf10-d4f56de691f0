"use strict";
/*************************************************************************************************

Welcome to Baml! To use this generated code, please run one of the following:

$ npm install @boundaryml/baml
$ yarn add @boundaryml/baml
$ pnpm add @boundaryml/baml

*************************************************************************************************/
Object.defineProperty(exports, "__esModule", { value: true });
exports.b = exports.BamlSyncClient = void 0;
const baml_1 = require("@boundaryml/baml");
const sync_request_1 = require("./sync_request");
const parser_1 = require("./parser");
const globals_1 = require("./globals");
class BamlSyncClient {
    constructor(runtime, ctxManager, bamlOptions) {
        this.runtime = runtime;
        this.ctxManager = ctxManager;
        this.httpRequest = new sync_request_1.HttpRequest(runtime, ctxManager);
        this.httpStreamRequest = new sync_request_1.HttpStreamRequest(runtime, ctxManager);
        this.llmResponseParser = new parser_1.LlmResponseParser(runtime, ctxManager);
        this.llmStreamParser = new parser_1.LlmStreamParser(runtime, ctxManager);
        this.bamlOptions = bamlOptions || {};
    }
    withOptions(bamlOptions) {
        return new BamlSyncClient(this.runtime, this.ctxManager, bamlOptions);
    }
    /*
    * @deprecated NOT IMPLEMENTED as streaming must by async. We
    * are not providing an async version as we want to reserve the
    * right to provide a sync version in the future.
    */
    get stream() {
        throw new Error("stream is not available in BamlSyncClient. Use `import { b } from 'baml_client/async_client");
    }
    get request() {
        return this.httpRequest;
    }
    get streamRequest() {
        return this.httpStreamRequest;
    }
    get parse() {
        return this.llmResponseParser;
    }
    get parseStream() {
        return this.llmStreamParser;
    }
    DetermineNextStep(thread, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = this.runtime.callFunctionSync("DetermineNextStep", {
                "thread": thread
            }, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return raw.parsed(false);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ExtractWebData(content, data_type, selector, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = this.runtime.callFunctionSync("ExtractWebData", {
                "content": content, "data_type": data_type, "selector": selector ?? null
            }, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return raw.parsed(false);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    ProcessWebContent(content, instruction, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = this.runtime.callFunctionSync("ProcessWebContent", {
                "content": content, "instruction": instruction
            }, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return raw.parsed(false);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
    SummarizeWebContent(content, max_length, focus_area, __baml_options__) {
        try {
            const options = { ...this.bamlOptions, ...(__baml_options__ || {}) };
            const collector = options.collector ? (Array.isArray(options.collector) ? options.collector : [options.collector]) : [];
            const rawEnv = __baml_options__?.env ? { ...process.env, ...__baml_options__.env } : { ...process.env };
            const env = Object.fromEntries(Object.entries(rawEnv).filter(([_, value]) => value !== undefined));
            const raw = this.runtime.callFunctionSync("SummarizeWebContent", {
                "content": content, "max_length": max_length ?? null, "focus_area": focus_area ?? null
            }, this.ctxManager.cloneContext(), options.tb?.__tb(), options.clientRegistry, collector, env);
            return raw.parsed(false);
        }
        catch (error) {
            throw (0, baml_1.toBamlError)(error);
        }
    }
}
exports.BamlSyncClient = BamlSyncClient;
exports.b = new BamlSyncClient(globals_1.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_RUNTIME, globals_1.DO_NOT_USE_DIRECTLY_UNLESS_YOU_KNOW_WHAT_YOURE_DOING_CTX);
//# sourceMappingURL=sync_client.js.map