"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebFetcher = exports.Thread = exports.WebFetchAgent = void 0;
const agent_1 = require("./agent");
Object.defineProperty(exports, "WebFetchAgent", { enumerable: true, get: function () { return agent_1.WebFetchAgent; } });
Object.defineProperty(exports, "Thread", { enumerable: true, get: function () { return agent_1.Thread; } });
const webFetcher_1 = require("./webFetcher");
Object.defineProperty(exports, "WebFetcher", { enumerable: true, get: function () { return webFetcher_1.WebFetcher; } });
// Example usage of the Web Fetch Tool
async function example() {
    console.log('🌐 Web Fetch Tool Example');
    console.log('='.repeat(40));
    const agent = new agent_1.WebFetchAgent();
    // Example 1: Fetch and summarize a webpage
    console.log('\n📖 Example 1: Fetch and summarize a webpage');
    const thread1 = new agent_1.Thread();
    thread1.addEvent('user_input', {
        data: 'Please fetch the content from https://example.com and provide a summary'
    });
    try {
        const result1 = await agent.agentLoop(thread1);
        console.log('Result:', result1);
    }
    catch (error) {
        console.error('Error in example 1:', error.message);
    }
    // Example 2: Extract links from a webpage
    console.log('\n🔗 Example 2: Extract links from a webpage');
    const thread2 = new agent_1.Thread();
    thread2.addEvent('user_input', {
        data: 'Fetch https://news.ycombinator.com and extract all the links'
    });
    try {
        const result2 = await agent.agentLoop(thread2);
        console.log('Result:', result2);
    }
    catch (error) {
        console.error('Error in example 2:', error.message);
    }
    // Example 3: Direct WebFetcher usage
    console.log('\n🔧 Example 3: Direct WebFetcher usage');
    const fetcher = new webFetcher_1.WebFetcher();
    try {
        const fetchResult = await fetcher.fetchUrl('https://httpbin.org/json');
        if (fetchResult.success) {
            console.log('Fetched content length:', fetchResult.content?.length);
            console.log('Metadata:', fetchResult.metadata);
        }
        else {
            console.log('Fetch failed:', fetchResult.errorMessage);
        }
    }
    catch (error) {
        console.error('Error in example 3:', error.message);
    }
}
// Run example if this file is executed directly
if (require.main === module) {
    example().catch(console.error);
}
//# sourceMappingURL=index.js.map