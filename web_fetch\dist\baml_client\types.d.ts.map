{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../baml_client/types.ts"], "names": [], "mappings": "AAAA;;;;;;;;kGAQkG;AAalG;;;;;GAKG;AACH,MAAM,MAAM,oBAAoB,CAAC,CAAC,IAAI,CAAC,SAAS,MAAM,GAChD;KAAG,CAAC,IAAI,MAAM,CAAC,CAAC,CAAC,EAAE,oBAAoB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;CAAE,GAC/C,CAAC,GAAG,IAAI,CAAC;AAEf,MAAM,WAAW,OAAO,CAAC,CAAC,EAAC,SAAS,SAAS,MAAM,GAAG,MAAM;IACxD,KAAK,EAAE,CAAC,CAAC;IACT,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,CAAC;CACpC;AAED,MAAM,WAAW,KAAK;IAClB,IAAI,EAAE,MAAM,CAAC;IACb,IAAI,EAAE,MAAM,CAAA;IACZ,MAAM,EAAE,WAAW,GAAG,QAAQ,CAAA;CACjC;AAED,wBAAgB,aAAa,CAAC,SAAS,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,OAAO,CAEjG;AAED,wBAAgB,UAAU,CAAC,SAAS,SAAS,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,SAAS,EAAE,KAAK,CAAC,GAAG,KAAK,EAAE,CAE9F;AACD,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,cAAc,CAAA;IACtB,OAAO,EAAE,MAAM,CAAA;CAEhB;AAED,MAAM,WAAW,eAAe;IAC9B,MAAM,EAAE,cAAc,CAAA;IACtB,OAAO,EAAE,MAAM,CAAA;IACf,SAAS,EAAE,MAAM,CAAA;IACjB,QAAQ,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;CAEzB;AAED,MAAM,WAAW,YAAY;IAC3B,MAAM,EAAE,WAAW,CAAA;IACnB,GAAG,EAAE,MAAM,CAAA;IACX,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;CAE5B;AAED,MAAM,WAAW,kBAAkB;IACjC,MAAM,EAAE,iBAAiB,CAAA;IACzB,OAAO,EAAE,MAAM,CAAA;IACf,WAAW,EAAE,MAAM,CAAA;IACnB,MAAM,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;CAEvB;AAED,MAAM,WAAW,oBAAoB;IACnC,MAAM,EAAE,mBAAmB,CAAA;IAC3B,OAAO,EAAE,MAAM,CAAA;IACf,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAC1B,UAAU,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;CAE3B;AAED,MAAM,WAAW,cAAc;IAC7B,OAAO,EAAE,OAAO,CAAA;IAChB,OAAO,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACvB,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAC7B,QAAQ,CAAC,EAAE,WAAW,GAAG,IAAI,CAAA;CAE9B;AAED,MAAM,WAAW,WAAW;IAC1B,KAAK,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IACrB,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAC3B,GAAG,EAAE,MAAM,CAAA;IACX,YAAY,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAC5B,cAAc,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAC9B,aAAa,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;IAC7B,WAAW,CAAC,EAAE,MAAM,GAAG,IAAI,CAAA;CAE5B;AAED,MAAM,MAAM,aAAa,GAAG,YAAY,GAAG,kBAAkB,GAAG,eAAe,GAAG,oBAAoB,CAAA"}